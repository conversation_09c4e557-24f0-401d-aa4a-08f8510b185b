<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>lisador de Vídeos do YouTube com Gemini</title>
    <!-- Adicionando Marked.js via CDN -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        :root {
            --primary-color: #4285f4;
            --secondary-color: #34a853;
            --accent-color: #ea4335;
            --background-color: #f8f9fa;
            --text-color: #202124;
        }

        body {
            font-family: 'Roboto', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }

        textarea {
            min-height: 120px;
            resize: vertical;
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            display: block;
            width: 100%;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #3367d6;
        }

        button:disabled {
            background-color: #a9a9a9;
            cursor: not-allowed;
        }

        .result {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }

        .result h2 {
            color: var(--secondary-color);
            margin-bottom: 15px;
        }

        .response-container {
            background-color: #f5f5f5;
            border-radius: 4px;
            padding: 15px;
            white-space: pre-wrap;
            overflow-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
        }

        /* Estilos adicionais para melhorar a aparência do markdown renderizado */
        .response-container h1, 
        .response-container h2, 
        .response-container h3 {
            margin-top: 1em;
            margin-bottom: 0.5em;
            color: var(--primary-color);
        }

        .response-container ul, 
        .response-container ol {
            padding-left: 2em;
        }

        .response-container blockquote {
            border-left: 3px solid var(--secondary-color);
            padding-left: 1em;
            margin-left: 0;
            color: #555;
        }

        .response-container code {
            background-color: #e8e8e8;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }

        .response-container pre {
            background-color: #e8e8e8;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }

        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }

        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 4px solid var(--primary-color);
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: var(--accent-color);
            margin-top: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Analisador de Vídeos do YouTube com Gemini</h1>
        
        <div class="form-group">
            <label for="youtube-url">URL do Vídeo do YouTube:</label>
            <input type="text" id="youtube-url" placeholder="https://www.youtube.com/watch?v=..." required>
        </div>
        
        <div class="form-group">
            <label for="api-key">Chave da API Gemini:</label>
            <input type="text" id="api-key" placeholder="Insira sua chave da API Gemini" required>
        </div>
        
        <div class="form-group">
            <label for="prompt">Prompt para o Gemini:</label>
            <textarea id="prompt" placeholder="Descreva o que você quer que o Gemini analise no vídeo. Ex: Faça um resumo dos principais pontos deste vídeo." required></textarea>
        </div>
        
        <button id="analyze-btn">Analisar Vídeo</button>
        
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <p>Processando o vídeo. Isso pode levar alguns instantes...</p>
        </div>
        
        <div class="result" id="result" style="display: none;">
            <h2>Resultado da Análise</h2>
            <div class="response-container" id="response"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const analyzeBtn = document.getElementById('analyze-btn');
            const youtubeUrlInput = document.getElementById('youtube-url');
            const apiKeyInput = document.getElementById('api-key');
            const promptInput = document.getElementById('prompt');
            const loadingDiv = document.getElementById('loading');
            const resultDiv = document.getElementById('result');
            const responseContainer = document.getElementById('response');
            
            analyzeBtn.addEventListener('click', async function() {
                // Validar entradas
                const youtubeUrl = youtubeUrlInput.value.trim();
                const apiKey = apiKeyInput.value.trim();
                const prompt = promptInput.value.trim();
                
                if (!youtubeUrl || !apiKey || !prompt) {
                    alert('Por favor, preencha todos os campos.');
                    return;
                }
                
                // Extrair o ID do vídeo
                const videoId = getVideoId(youtubeUrl);
                if (!videoId) {
                    alert('URL do YouTube inválida. Por favor, insira uma URL válida.');
                    return;
                }
                
                // Mostrar loading e esconder resultados anteriores
                loadingDiv.style.display = 'block';
                resultDiv.style.display = 'none';
                analyzeBtn.disabled = true;
                
                try {
                    const response = await sendToGemini(videoId, prompt, apiKey);
                    
                    // Exibir resultado
                    responseContainer.innerHTML = response;
                    resultDiv.style.display = 'block';
                } catch (error) {
                    responseContainer.innerHTML = `<div class="error">Erro: ${error.message}</div>`;
                    resultDiv.style.display = 'block';
                } finally {
                    loadingDiv.style.display = 'none';
                    analyzeBtn.disabled = false;
                }
            });
            
            // Função para extrair o ID do vídeo da URL
            function getVideoId(url) {
                try {
                    const urlObj = new URL(url);
                    
                    // Formato padrão: youtube.com/watch?v=VIDEO_ID
                    if (urlObj.hostname.includes('youtube.com')) {
                        return urlObj.searchParams.get('v');
                    }
                    
                    // Formato curto: youtu.be/VIDEO_ID
                    if (urlObj.hostname === 'youtu.be') {
                        return urlObj.pathname.substring(1);
                    }
                    
                    return null;
                } catch (error) {
                    console.error("Erro ao extrair ID do vídeo:", error);
                    return null;
                }
            }
            
            // Função para enviar o vídeo e prompt para a API Gemini
            async function sendToGemini(videoId, userPrompt, apiKey) {
                const url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
                
                // System prompt para melhorar as respostas
                const systemPrompt = {
                    role: "system",
                    parts: [{
                        text: `Você é um assistente especializado em análise de conteúdo de vídeos do YouTube. 
                        Sua tarefa é analisar o vídeo fornecido e responder à pergunta do usuário de forma clara, 
                        estruturada e informativa. Sempre responda em Português do Brasil.
                        
                        Ao analisar o vídeo:
                        1. Identifique os principais tópicos e temas abordados
                        2. Extraia informações relevantes do conteúdo visual e de áudio
                        3. Formate sua resposta com marcadores, parágrafos e seções quando apropriado
                        4. Seja objetivo e direto, mas forneça detalhes importantes
                        5. Se o vídeo contiver informações técnicas, explique-as de forma acessível
                        
                        Use formatação Markdown para estruturar sua resposta:
                        - Use # para títulos principais
                        - Use ## e ### para subtítulos
                        - Use **texto** para negrito
                        - Use *texto* para itálico
                        - Use listas com - ou números
                        - Use > para citações
                        - Use \`código\` para termos técnicos
                        
                        Sua resposta deve ser útil, informativa e adaptada especificamente ao conteúdo do vídeo analisado.`
                    }]
                };
                
                const payload = {
                    contents: [{
                        parts: [
                            {
                                "fileData": {
                                    "fileUri": `https://youtu.be/${videoId}`,
                                    "mimeType": "video/*"
                                }
                            },
                            {
                                text: userPrompt
                            }
                        ]
                    }],
                    systemInstruction: systemPrompt
                };
                
                try {
                    const response = await fetch(`${url}?key=${apiKey}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    
                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Erro na API (${response.status}): ${errorText}`);
                    }
                    
                    const responseData = await response.json();
                    
                    if (!responseData.candidates || !responseData.candidates[0]?.content?.parts[0]?.text) {
                        throw new Error("Resposta inválida da API");
                    }
                    
                    // Obter o conteúdo da resposta
                    const content = responseData.candidates[0].content.parts[0].text;
                    
                    // Converter Markdown para HTML usando a biblioteca marked
                    return marked.parse(content);
                } catch (error) {
                    console.error("Erro na requisição Gemini:", error);
                    throw error;
                }
            }
        });
    </script>
</body>
</html>