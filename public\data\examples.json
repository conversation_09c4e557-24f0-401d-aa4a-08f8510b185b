[{"title": "How chords work", "url": "https://www.youtube.com/watch?v=JfD0nHrJDC0", "spec": "Build me an interactive web app to help a learner understand triads and their inversions.\n\nSPECIFICATIONS:\n1.  The app must feature an interactive keyboard. When a key is pressed, the corresponding note should sound.\n2.  The app must allow the user to select a root note (C, D, E, F, G, A, or B).  This root note should be displayed clearly. The root note should default to C.\n3. The app must allow the user to select a triad type (Major, Minor, Diminished, Augmented). The triad type should be displayed clearly. The triad type should default to Major.\n4. The app must allow the user to select an inversion (Root position, First Inversion, Second Inversion). The inversion should be displayed clearly. The inversion should default to Root Position.\n5. Based on the selected root, triad type, and inversion, the app must highlight the corresponding notes on the keyboard.  The notes should also sound.\n6. The app must display the formula for generating the triad in terms of intervals (e.g., for C Major, it should display 'Root: C, Third: Major Third, Fifth: Perfect Fifth'). For inversions, it should explicitly state which note is the lowest (e.g., 'Lowest note is Third').\n7.  The app must be responsive and work well on both desktop and mobile devices.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Triads and Inversions</title>\n    <style>\n        /* Basic Reset & Body Styling */\n        * {\n            box-sizing: border-box;\n            margin: 0;\n            padding: 0;\n        }\n\n        body {\n            font-family: sans-serif;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            padding: 20px;\n            background-color: #f0f0f0;\n            color: #333;\n        }\n\n        /* Main Container */\n        .app-container {\n            background-color: #fff;\n            padding: 20px;\n            border-radius: 8px;\n            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n            width: 100%;\n            max-width: 800px; /* Limit max width on desktop */\n            margin-bottom: 20px;\n        }\n\n        h1 {\n            text-align: center;\n            margin-bottom: 20px;\n            color: #2c3e50;\n        }\n\n        /* Controls Section */\n        .controls {\n            display: flex;\n            flex-wrap: wrap; /* Allow wrapping on smaller screens */\n            justify-content: space-around;\n            margin-bottom: 25px;\n            gap: 15px; /* Spacing between control groups */\n        }\n\n        .control-group {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n        }\n\n        .control-group label {\n            margin-bottom: 5px;\n            font-weight: bold;\n            color: #555;\n        }\n\n        .control-group select {\n            padding: 8px 12px;\n            border: 1px solid #ccc;\n            border-radius: 4px;\n            min-width: 120px; /* Ensure decent width */\n            background-color: #fff;\n            cursor: pointer;\n        }\n\n        /* Info Display Section */\n        .info-display {\n            background-color: #e9ecef;\n            padding: 15px;\n            border-radius: 5px;\n            margin-bottom: 25px;\n            border: 1px solid #ced4da;\n        }\n\n        .info-display p {\n            margin-bottom: 8px;\n            line-height: 1.5;\n        }\n\n        .info-display strong {\n           color: #343a40;\n        }\n\n        /* Keyboard Section */\n        .keyboard-container {\n            width: 100%;\n            margin: 0 auto; /* Center the keyboard */\n            overflow-x: auto; /* Allow horizontal scroll if needed on very small screens */\n            padding-bottom: 10px; /* Space for scrollbar if it appears */\n        }\n\n        .keyboard {\n            display: flex;\n            position: relative;\n            height: 180px; /* Adjust height as needed */\n            width: max-content; /* Allow keyboard to determine its own width */\n            min-width: 100%; /* Ensure it tries to fill container */\n            margin: 0 auto; /* Center within container */\n            border: 2px solid #555;\n            border-radius: 5px;\n            background-color: #333; /* Background visible between keys */\n        }\n\n        .key {\n            cursor: pointer;\n            border: 1px solid #555;\n            transition: background-color 0.1s ease;\n            display: flex;\n            align-items: flex-end; /* Position note name at bottom */\n            justify-content: center;\n            padding-bottom: 5px;\n            font-size: 0.8em;\n            position: relative; /* Needed for absolute positioning of black keys */\n        }\n\n        .key.white {\n            background-color: #ffffff;\n            width: 50px; /* Adjust width */\n            height: 100%;\n            color: #333;\n            border-bottom-left-radius: 4px;\n            border-bottom-right-radius: 4px;\n            z-index: 1; /* White keys below black keys */\n        }\n\n        .key.black {\n            background-color: #333333;\n            width: 30px; /* Adjust width */\n            height: 60%;\n            color: #ffffff;\n            position: absolute;\n            top: 0;\n            margin-left: -15px; /* Half its width to center over white key boundary */\n            z-index: 2; /* Black keys above white keys */\n            border-bottom-left-radius: 3px;\n            border-bottom-right-radius: 3px;\n        }\n\n        /* Specific positioning for black keys */\n        .key[data-note^=\"C#\"], .key[data-note^=\"Db\"] { margin-left: -15px; left: 50px; }\n        .key[data-note^=\"D#\"], .key[data-note^=\"Eb\"] { margin-left: -15px; left: 100px; }\n        .key[data-note^=\"F#\"], .key[data-note^=\"Gb\"] { margin-left: -15px; left: 200px; }\n        .key[data-note^=\"G#\"], .key[data-note^=\"Ab\"] { margin-left: -15px; left: 250px; }\n        .key[data-note^=\"A#\"], .key[data-note^=\"Bb\"] { margin-left: -15px; left: 300px; }\n\n        /* Adjust positions for subsequent octaves */\n        .key[data-note^=\"C#5\"], .key[data-note^=\"Db5\"] { left: calc(50px + 7 * 50px); }\n        .key[data-note^=\"D#5\"], .key[data-note^=\"Eb5\"] { left: calc(100px + 7 * 50px); }\n        .key[data-note^=\"F#5\"], .key[data-note^=\"Gb5\"] { left: calc(200px + 7 * 50px); }\n        .key[data-note^=\"G#5\"], .key[data-note^=\"Ab5\"] { left: calc(250px + 7 * 50px); }\n        .key[data-note^=\"A#5\"], .key[data-note^=\"Bb5\"] { left: calc(300px + 7 * 50px); }\n\n        .key:active, .key.pressed {\n            background-color: #a0a0a0; /* Visual feedback on press */\n        }\n        .key.white:active, .key.white.pressed { background-color: #d0d0d0; }\n        .key.black:active, .key.black.pressed { background-color: #666666; }\n\n        .key.highlighted {\n            background-color: #4a90e2; /* Highlight color for triad notes */\n            color: white;\n            border: 2px solid #1a5dab;\n        }\n        .key.black.highlighted {\n             background-color: #3a7bc8;\n             color: white;\n             border: 1px solid #1a5dab;\n        }\n\n         /* Responsive Adjustments */\n        @media (max-width: 768px) {\n            .key.white { width: 40px; }\n            .key.black { width: 24px; margin-left: -12px; }\n            /* Adjust black key positions based on new white key width */\n            .key[data-note^=\"C#\"], .key[data-note^=\"Db\"] { left: 40px; }\n            .key[data-note^=\"D#\"], .key[data-note^=\"Eb\"] { left: 80px; }\n            .key[data-note^=\"F#\"], .key[data-note^=\"Gb\"] { left: 160px; }\n            .key[data-note^=\"G#\"], .key[data-note^=\"Ab\"] { left: 200px; }\n            .key[data-note^=\"A#\"], .key[data-note^=\"Bb\"] { left: 240px; }\n            .key[data-note^=\"C#5\"], .key[data-note^=\"Db5\"] { left: calc(40px + 7 * 40px); }\n            .key[data-note^=\"D#5\"], .key[data-note^=\"Eb5\"] { left: calc(80px + 7 * 40px); }\n            .key[data-note^=\"F#5\"], .key[data-note^=\"Gb5\"] { left: calc(160px + 7 * 40px); }\n            .key[data-note^=\"G#5\"], .key[data-note^=\"Ab5\"] { left: calc(200px + 7 * 40px); }\n            .key[data-note^=\"A#5\"], .key[data-note^=\"Bb5\"] { left: calc(240px + 7 * 40px); }\n\n            .keyboard { height: 150px; }\n            .key.black { height: 55%; }\n            .key { font-size: 0.7em; }\n        }\n\n         @media (max-width: 500px) {\n            body { padding: 10px; }\n            .app-container { padding: 15px; }\n            h1 { font-size: 1.5em; }\n            .controls { flex-direction: column; align-items: stretch; gap: 10px; }\n            .control-group select { width: 100%; }\n\n            .key.white { width: 35px; }\n            .key.black { width: 21px; margin-left: -10.5px; }\n            /* Adjust black key positions based on new white key width */\n            .key[data-note^=\"C#\"], .key[data-note^=\"Db\"] { left: 35px; }\n            .key[data-note^=\"D#\"], .key[data-note^=\"Eb\"] { left: 70px; }\n            .key[data-note^=\"F#\"], .key[data-note^=\"Gb\"] { left: 140px; }\n            .key[data-note^=\"G#\"], .key[data-note^=\"Ab\"] { left: 175px; }\n            .key[data-note^=\"A#\"], .key[data-note^=\"Bb\"] { left: 210px; }\n            .key[data-note^=\"C#5\"], .key[data-note^=\"Db5\"] { left: calc(35px + 7 * 35px); }\n            .key[data-note^=\"D#5\"], .key[data-note^=\"Eb5\"] { left: calc(70px + 7 * 35px); }\n            .key[data-note^=\"F#5\"], .key[data-note^=\"Gb5\"] { left: calc(140px + 7 * 35px); }\n            .key[data-note^=\"G#5\"], .key[data-note^=\"Ab5\"] { left: calc(175px + 7 * 35px); }\n            .key[data-note^=\"A#5\"], .key[data-note^=\"Bb5\"] { left: calc(210px + 7 * 35px); }\n\n            .keyboard { height: 130px; }\n            .key.black { height: 50%; }\n            .key { font-size: 0.6em; padding-bottom: 3px; }\n        }\n\n    </style>\n</head>\n<body>\n\n    <div class=\"app-container\">\n        <h1>Triads and Inversions</h1>\n\n        <div class=\"controls\">\n            <div class=\"control-group\">\n                <label for=\"root-select\">Root Note:</label>\n                <select id=\"root-select\">\n                    <option value=\"C\">C</option>\n                    <option value=\"D\">D</option>\n                    <option value=\"E\">E</option>\n                    <option value=\"F\">F</option>\n                    <option value=\"G\">G</option>\n                    <option value=\"A\">A</option>\n                    <option value=\"B\">B</option>\n                </select>\n            </div>\n            <div class=\"control-group\">\n                <label for=\"triad-select\">Triad Type:</label>\n                <select id=\"triad-select\">\n                    <option value=\"Major\">Major</option>\n                    <option value=\"Minor\">Minor</option>\n                    <option value=\"Diminished\">Diminished</option>\n                    <option value=\"Augmented\">Augmented</option>\n                </select>\n            </div>\n            <div class=\"control-group\">\n                <label for=\"inversion-select\">Inversion:</label>\n                <select id=\"inversion-select\">\n                    <option value=\"Root Position\">Root Position</option>\n                    <option value=\"First Inversion\">First Inversion</option>\n                    <option value=\"Second Inversion\">Second Inversion</option>\n                </select>\n            </div>\n        </div>\n\n        <div class=\"info-display\">\n            <p><strong>Selection:</strong> <span id=\"selection-info\">C Major, Root Position</span></p>\n            <p><strong>Formula:</strong> <span id=\"formula-info\">Root: C, Third: Major Third (E), Fifth: Perfect Fifth (G)</span></p>\n            <p><strong>Notes:</strong> <span id=\"notes-info\">C4, E4, G4</span></p>\n            <p id=\"lowest-note-info-container\" style=\"display: none;\"><strong>Lowest Note:</strong> <span id=\"lowest-note-info\"></span></p>\n        </div>\n\n        <div class=\"keyboard-container\">\n            <div class=\"keyboard\" id=\"keyboard\">\n                <!-- Keys will be generated by JavaScript -->\n            </div>\n        </div>\n    </div>\n\n    <script>\n        document.addEventListener('DOMContentLoaded', () => {\n            const keyboardDiv = document.getElementById('keyboard');\n            const rootSelect = document.getElementById('root-select');\n            const triadSelect = document.getElementById('triad-select');\n            const inversionSelect = document.getElementById('inversion-select');\n            const selectionInfo = document.getElementById('selection-info');\n            const formulaInfo = document.getElementById('formula-info');\n            const notesInfo = document.getElementById('notes-info');\n            const lowestNoteInfoContainer = document.getElementById('lowest-note-info-container');\n            const lowestNoteInfo = document.getElementById('lowest-note-info');\n\n            let audioContext; // Initialize on first interaction\n            const oscillators = {}; // Keep track of playing notes for stopping\n\n            // --- Audio Setup ---\n            function initAudioContext() {\n                if (!audioContext) {\n                    try {\n                        audioContext = new (window.AudioContext || window.webkitAudioContext)();\n                    } catch (e) {\n                        console.error(\"Web Audio API is not supported in this browser\", e);\n                        alert(\"Web Audio API is not supported in this browser. Sound playback will not work.\");\n                    }\n                }\n            }\n\n            function noteToFrequency(note) {\n                const noteFrequencies = {\n                    'C3': 130.81, 'C#3': 138.59, 'Db3': 138.59, 'D3': 146.83, 'D#3': 155.56, 'Eb3': 155.56, 'E3': 164.81, 'F3': 174.61, 'F#3': 185.00, 'Gb3': 185.00, 'G3': 196.00, 'G#3': 207.65, 'Ab3': 207.65, 'A3': 220.00, 'A#3': 233.08, 'Bb3': 233.08, 'B3': 246.94,\n                    'C4': 261.63, 'C#4': 277.18, 'Db4': 277.18, 'D4': 293.66, 'D#4': 311.13, 'Eb4': 311.13, 'E4': 329.63, 'F4': 349.23, 'F#4': 369.99, 'Gb4': 369.99, 'G4': 392.00, 'G#4': 415.30, 'Ab4': 415.30, 'A4': 440.00, 'A#4': 466.16, 'Bb4': 466.16, 'B4': 493.88,\n                    'C5': 523.25, 'C#5': 554.37, 'Db5': 554.37, 'D5': 587.33, 'D#5': 622.25, 'Eb5': 622.25, 'E5': 659.25, 'F5': 698.46, 'F#5': 739.99, 'Gb5': 739.99, 'G5': 783.99, 'G#5': 830.61, 'Ab5': 830.61, 'A5': 880.00, 'A#5': 932.33, 'Bb5': 932.33, 'B5': 987.77,\n                    'C6': 1046.50\n                };\n                return noteFrequencies[note];\n            }\n\n            function playNote(note, duration = 0.6) {\n                initAudioContext(); // Ensure context is ready\n                if (!audioContext) return;\n\n                const freq = noteToFrequency(note);\n                if (!freq) {\n                    console.warn(`Frequency not found for note: ${note}`);\n                    return;\n                }\n\n                // Stop existing oscillator for this note if any\n                if (oscillators[note]) {\n                    try {\n                         oscillators[note].stop();\n                    } catch(e) { /* Ignore errors if already stopped */ }\n                    delete oscillators[note];\n                }\n\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n\n                oscillator.type = 'sine'; // 'sine', 'square', 'sawtooth', 'triangle'\n                oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);\n                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime); // Start with volume\n\n                // Fade out\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration * 0.9);\n\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n\n                oscillator.start(audioContext.currentTime);\n                oscillators[note] = oscillator; // Store reference\n\n                // Schedule stop\n                oscillator.stop(audioContext.currentTime + duration);\n                 // Clean up reference after stop\n                setTimeout(() => {\n                     if (oscillators[note] === oscillator) {\n                         delete oscillators[note];\n                     }\n                }, duration * 1000);\n            }\n\n            function playChord(notes) {\n                 initAudioContext(); // Ensure context is ready before playing\n                 if (!audioContext) return;\n                 notes.forEach(note => playNote(note, 1.0)); // Play chord notes for longer duration\n            }\n\n            // --- Music Theory Logic ---\n            const notesSharp = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];\n            const notesFlat = ['C', 'Db', 'D', 'Eb', 'E', 'F', 'Gb', 'G', 'Ab', 'A', 'Bb', 'B'];\n\n            // Use flats for certain keys for conventional spelling\n            const shouldUseFlats = (rootNote) => ['F', 'Bb', 'Eb', 'Ab', 'Db', 'Gb'].includes(rootNote) || rootNote.includes('b');\n\n            // Function to get note name based on index and preference for sharp/flat\n            function getNoteName(index, preferFlat = false) {\n                const noteSet = preferFlat ? notesFlat : notesSharp;\n                return noteSet[index % 12];\n            }\n\n            // Get semitone index from root (0-11)\n            function getNoteIndex(noteName) {\n                let index = notesSharp.indexOf(noteName);\n                if (index === -1) index = notesFlat.indexOf(noteName);\n                return index;\n            }\n\n            // Calculate triad notes relative to root index\n            function calculateTriadIntervals(triadType) {\n                switch (triadType) {\n                    case 'Major': return { third: 4, fifth: 7, thirdName: \"Major Third\", fifthName: \"Perfect Fifth\" };\n                    case 'Minor': return { third: 3, fifth: 7, thirdName: \"Minor Third\", fifthName: \"Perfect Fifth\" };\n                    case 'Diminished': return { third: 3, fifth: 6, thirdName: \"Minor Third\", fifthName: \"Diminished Fifth\" };\n                    case 'Augmented': return { third: 4, fifth: 8, thirdName: \"Major Third\", fifthName: \"Augmented Fifth\" };\n                    default: return { third: 4, fifth: 7, thirdName: \"Major Third\", fifthName: \"Perfect Fifth\" }; // Default to Major\n                }\n            }\n\n            // Get specific note names with octave\n            function getTriadNotes(rootNote, triadType, startOctave = 4) {\n                const rootIndex = getNoteIndex(rootNote);\n                const intervals = calculateTriadIntervals(triadType);\n                const preferFlat = shouldUseFlats(rootNote);\n\n                const rootName = getNoteName(rootIndex, preferFlat);\n                const thirdIndex = rootIndex + intervals.third;\n                const fifthIndex = rootIndex + intervals.fifth;\n\n                const thirdName = getNoteName(thirdIndex, preferFlat);\n                const fifthName = getNoteName(fifthIndex, preferFlat);\n\n                // Basic octave assignment (adjust if note wraps past B)\n                const rootOctave = startOctave;\n                const thirdOctave = thirdIndex >= 12 ? startOctave + 1 : startOctave;\n                const fifthOctave = fifthIndex >= 12 ? startOctave + 1 : startOctave;\n\n                return [\n                    { name: rootName, octave: rootOctave, note: `${rootName}${rootOctave}`, role: \"Root\" },\n                    { name: thirdName, octave: thirdOctave, note: `${thirdName}${thirdOctave}`, role: \"Third\" },\n                    { name: fifthName, octave: fifthOctave, note: `${fifthName}${fifthOctave}`, role: \"Fifth\" }\n                ];\n            }\n\n            function applyInversion(rootPositionNotes, inversionType) {\n                const notes = [...rootPositionNotes]; // Copy array\n                let invertedNotes = [];\n                let lowestNoteRole = \"Root\";\n\n                switch (inversionType) {\n                    case 'First Inversion':\n                        // Third becomes lowest note. Root moves up an octave.\n                        const root = notes[0];\n                        const third = notes[1];\n                        const fifth = notes[2];\n                        invertedNotes = [\n                            { ...third }, // Keep original third object\n                            { ...fifth }, // Keep original fifth object\n                            { ...root, octave: root.octave + 1, note: `${root.name}${root.octave + 1}` } // Root goes up\n                        ];\n                        lowestNoteRole = \"Third\";\n                        break;\n                    case 'Second Inversion':\n                        // Fifth becomes lowest note. Root and Third move up an octave.\n                        const root2 = notes[0];\n                        const third2 = notes[1];\n                        const fifth2 = notes[2];\n                         invertedNotes = [\n                            { ...fifth2 }, // Keep original fifth object\n                            { ...root2, octave: root2.octave + 1, note: `${root2.name}${root2.octave + 1}` }, // Root goes up\n                            { ...third2, octave: third2.octave + 1, note: `${third2.name}${third2.octave + 1}` } // Third goes up\n                        ];\n                         lowestNoteRole = \"Fifth\";\n                        break;\n                    case 'Root Position':\n                    default:\n                        invertedNotes = notes;\n                        lowestNoteRole = \"Root\";\n                        break;\n                }\n                 // Sort by frequency for consistent display/playing order if needed, though order matters for inversion definition\n                // For this app, the order defined above IS the inversion.\n                return { invertedNotes, lowestNoteRole };\n            }\n\n\n            // --- Keyboard Generation ---\n            function createKeyboard() {\n                keyboardDiv.innerHTML = ''; // Clear existing keys\n                const keys = [\n                    // Octave 4\n                    { note: 'C4', type: 'white' }, { note: 'C#4', type: 'black' }, { note: 'D4', type: 'white' }, { note: 'D#4', type: 'black' }, { note: 'E4', type: 'white' },\n                    { note: 'F4', type: 'white' }, { note: 'F#4', type: 'black' }, { note: 'G4', type: 'white' }, { note: 'G#4', type: 'black' }, { note: 'A4', type: 'white' }, { note: 'A#4', type: 'black' }, { note: 'B4', type: 'white' },\n                    // Octave 5\n                    { note: 'C5', type: 'white' }, { note: 'C#5', type: 'black' }, { note: 'D5', type: 'white' }, { note: 'D#5', type: 'black' }, { note: 'E5', type: 'white' },\n                    { note: 'F5', type: 'white' }, { note: 'F#5', type: 'black' }, { note: 'G5', type: 'white' }, { note: 'G#5', type: 'black' }, { note: 'A5', type: 'white' }, { note: 'A#5', type: 'black' }, { note: 'B5', type: 'white' },\n                     // Add C6 for range\n                    { note: 'C6', type: 'white' }\n                ];\n\n                keys.forEach(keyInfo => {\n                    const keyElement = document.createElement('div');\n                    keyElement.classList.add('key', keyInfo.type);\n                    keyElement.dataset.note = keyInfo.note;\n                    // Display note name without octave on key for simplicity, maybe add octave later if needed\n                    keyElement.textContent = keyInfo.note.slice(0, -1); // Show C, C#, D etc.\n\n                    // Add event listener for playing sound on click/touch\n                    keyElement.addEventListener('mousedown', (e) => {\n                        e.preventDefault(); // Prevent focus issues on mobile\n                        initAudioContext(); // Ensure audio context is started by user gesture\n                        playNote(keyInfo.note);\n                        keyElement.classList.add('pressed'); // Visual feedback\n                    });\n                    keyElement.addEventListener('mouseup', () => {\n                       keyElement.classList.remove('pressed');\n                    });\n                    keyElement.addEventListener('mouseleave', () => { // If mouse slides off while pressed\n                        keyElement.classList.remove('pressed');\n                    });\n                     // Touch events for mobile\n                     keyElement.addEventListener('touchstart', (e) => {\n                        e.preventDefault();\n                        initAudioContext();\n                        playNote(keyInfo.note);\n                        keyElement.classList.add('pressed');\n                    }, { passive: false }); // Need passive false to preventDefault\n                    keyElement.addEventListener('touchend', () => {\n                       keyElement.classList.remove('pressed');\n                    });\n\n\n                    keyboardDiv.appendChild(keyElement);\n                });\n            }\n\n            // --- Update UI ---\n            function updateDisplay() {\n                const root = rootSelect.value;\n                const type = triadSelect.value;\n                const inversion = inversionSelect.value;\n\n                // Update selection text\n                selectionInfo.textContent = `${root} ${type}, ${inversion}`;\n\n                // Calculate notes\n                const rootPositionNotes = getTriadNotes(root, type, 4); // Start at octave 4\n                const { invertedNotes, lowestNoteRole } = applyInversion(rootPositionNotes, inversion);\n                const notesToHighlight = invertedNotes.map(n => n.note);\n\n                 // Update formula text\n                const intervals = calculateTriadIntervals(type);\n                const rootPosNoteNames = rootPositionNotes.map(n => n.name);\n                formulaInfo.textContent = `Root: ${rootPosNoteNames[0]}, Third: ${intervals.thirdName} (${rootPosNoteNames[1]}), Fifth: ${intervals.fifthName} (${rootPosNoteNames[2]})`;\n\n                 // Update notes list text\n                 notesInfo.textContent = notesToHighlight.join(', ');\n\n                 // Update lowest note info\n                 if (inversion !== 'Root Position') {\n                    lowestNoteInfo.textContent = `${lowestNoteRole} (${invertedNotes[0].note})`;\n                    lowestNoteInfoContainer.style.display = 'block';\n                } else {\n                    lowestNoteInfoContainer.style.display = 'none';\n                }\n\n                // Highlight keys\n                highlightKeys(notesToHighlight);\n\n                // Play the chord\n                playChord(notesToHighlight);\n            }\n\n            function highlightKeys(notesToHighlight) {\n                // Clear previous highlights\n                document.querySelectorAll('.key.highlighted').forEach(key => {\n                    key.classList.remove('highlighted');\n                });\n\n                // Add new highlights\n                notesToHighlight.forEach(noteName => {\n                    // Handle enharmonics (e.g., C# and Db) - find either\n                    const sharpEquivalent = noteName.replace('b', '#');\n                    const flatEquivalent = noteName.replace('#', 'b');\n\n                    const keyElement = keyboardDiv.querySelector(`.key[data-note=\"${noteName}\"]`) ||\n                                       keyboardDiv.querySelector(`.key[data-note=\"${sharpEquivalent}\"]`) ||\n                                       keyboardDiv.querySelector(`.key[data-note=\"${flatEquivalent}\"]`);\n\n                    if (keyElement) {\n                        keyElement.classList.add('highlighted');\n                    } else {\n                        console.warn(`Key element not found for note: ${noteName}`);\n                    }\n                });\n            }\n\n            // --- Event Listeners ---\n            rootSelect.addEventListener('change', updateDisplay);\n            triadSelect.addEventListener('change', updateDisplay);\n            inversionSelect.addEventListener('change', updateDisplay);\n\n            // --- Initial Setup ---\n            createKeyboard();\n            updateDisplay(); // Initial display based on default values\n\n        });\n    </script>\n\n</body>\n</html>"}, {"title": "Understanding fractals", "url": "https://youtu.be/WFtTdf3I6Ug?si=8CO3POAroZcf9Vfj", "spec": "This app will help a learner understand the concept of fractals, as exemplified by the Koch snowflake. The core idea is that fractals are patterns that repeat themselves at different scales.\n\nSPECIFICATIONS:\n\n1.  The app should start with a single equilateral triangle in the center of the screen.\n2.  The app should have a button labeled \"Iterate\" or similar.\n3.  When the \"Iterate\" button is pressed, the app should perform one iteration of the Koch snowflake construction. This involves adding a new equilateral triangle to the middle of each side of the existing shape (pointing outward). The new triangles should be 1/3 the size of the sides they are attached to. The app should smoothly animate this process, so that the snowflake appears to grow.\n4.  The app should display a counter showing the number of iterations performed. The counter should start at zero.\n5.  The app should have a button labeled \"Reset\" or similar. When the \"Reset\" button is pressed, the app should return to the initial state with only the single equilateral triangle and the iteration counter set to zero.\n6.  The app should display the formula for calculating the number of sides after each iteration: 3 * (4 to the power of n), where n is the iteration number.\n7. The app should display the (calculated) number of sides after each iteration.\n8. The app should include a \"zoom\" feature, such that the user may zoom in on any particular area of the image.  When the user zooms in, the fractal nature of the image (i.e., self-similarity at different scales) should be clearly visible.\n9. The app should allow the user to change the color of the snowflake, and the background. The app should include a few pre-selected color schemes for ease of use.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Koch Snowflake Explorer</title>\n    <style>\n        body {\n            font-family: sans-serif;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            margin: 0;\n            padding: 10px;\n            background-color: #f0f0f0;\n            color: #333;\n        }\n        #canvasContainer {\n            position: relative; /* Needed for absolute positioning of overlay if added later */\n            border: 1px solid #ccc;\n            margin-bottom: 10px;\n            background-color: #fff; /* Default background */\n            cursor: grab;\n        }\n        #canvasContainer:active {\n             cursor: grabbing;\n        }\n        canvas {\n            display: block;\n        }\n        #controls {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 10px;\n            margin-bottom: 10px;\n            align-items: center;\n            justify-content: center;\n        }\n        button, select {\n            padding: 8px 15px;\n            font-size: 1em;\n            cursor: pointer;\n            border: 1px solid #ccc;\n            border-radius: 4px;\n            background-color: #e7e7e7;\n        }\n        button:hover, select:hover {\n            background-color: #ddd;\n        }\n        #info {\n            text-align: center;\n            margin-bottom: 10px;\n            font-size: 0.9em;\n            width: 100%;\n            max-width: 600px;\n        }\n        #info p {\n            margin: 5px 0;\n        }\n        .color-scheme-label {\n            margin-left: 15px;\n        }\n    </style>\n</head>\n<body>\n\n    <h1>Koch Snowflake Fractal Explorer</h1>\n\n    <div id=\"info\">\n        <p>Fractals are patterns that repeat themselves at different scales (self-similarity).</p>\n        <p>The Koch snowflake starts with an equilateral triangle. Each iteration adds a smaller triangle to the middle third of every side.</p>\n        <p>Use mouse wheel to zoom, click and drag to pan.</p>\n        <p>\n            Iterations: <span id=\"iterationCount\">0</span> |\n            Formula for sides: 3 * 4<sup>n</sup> |\n            Current Sides: <span id=\"sideCount\">3</span>\n        </p>\n    </div>\n\n    <div id=\"controls\">\n        <button id=\"iterateBtn\">Iterate</button>\n        <button id=\"resetBtn\">Reset</button>\n        <label for=\"colorSchemeSelect\" class=\"color-scheme-label\">Color Scheme:</label>\n        <select id=\"colorSchemeSelect\">\n            <option value=\"default\">Default (Blue on White)</option>\n            <option value=\"dark\">Dark Mode (Cyan on Black)</option>\n            <option value=\"forest\">Forest (Green on Beige)</option>\n            <option value=\"sunset\">Sunset (Orange on Dark Blue)</option>\n            <option value=\"mono\">Monochrome (White on Black)</option>\n        </select>\n        <!-- Optional direct color pickers (more advanced)\n        <label for=\"flakeColor\">Flake:</label>\n        <input type=\"color\" id=\"flakeColor\" value=\"#007bff\">\n        <label for=\"bgColor\">Background:</label>\n        <input type=\"color\" id=\"bgColor\" value=\"#ffffff\">\n        -->\n    </div>\n\n    <div id=\"canvasContainer\">\n        <canvas id=\"kochCanvas\" width=\"600\" height=\"550\"></canvas>\n    </div>\n\n    <script>\n        const canvas = document.getElementById('kochCanvas');\n        const ctx = canvas.getContext('2d');\n        const iterateBtn = document.getElementById('iterateBtn');\n        const resetBtn = document.getElementById('resetBtn');\n        const iterationCountSpan = document.getElementById('iterationCount');\n        const sideCountSpan = document.getElementById('sideCount');\n        const colorSchemeSelect = document.getElementById('colorSchemeSelect');\n        const canvasContainer = document.getElementById('canvasContainer');\n\n        // --- State Variables ---\n        let iteration = 0;\n        let segments = [];\n        let snowflakeColor = '#007bff'; // Default blue\n        let backgroundColor = '#ffffff'; // Default white\n        let zoomLevel = 1.0;\n        let offsetX = 0;\n        let offsetY = 0;\n        let isPanning = false;\n        let lastMouseX = 0;\n        let lastMouseY = 0;\n\n        // --- Color Schemes ---\n        const colorSchemes = {\n            default: { flake: '#007bff', bg: '#ffffff' },\n            dark:    { flake: '#00ffff', bg: '#000000' },\n            forest:  { flake: '#228B22', bg: '#F5F5DC' },\n            sunset:  { flake: '#FFA500', bg: '#00008B' },\n            mono:    { flake: '#ffffff', bg: '#000000' },\n        };\n\n        // --- Core Koch Logic ---\n        function getInitialTriangle(width, height) {\n            const side = Math.min(width, height) * 0.5; // Size relative to canvas\n            const h = side * Math.sqrt(3) / 2; // Height of equilateral triangle\n            const cx = width / 2;\n            const cy = height / 2 + h / 3; // Center slightly lower\n\n            const p1 = { x: cx, y: cy - 2 * h / 3 };\n            const p2 = { x: cx - side / 2, y: cy + h / 3 };\n            const p3 = { x: cx + side / 2, y: cy + h / 3 };\n\n            // Store segments as [startPoint, endPoint]\n            return [ [p1, p2], [p2, p3], [p3, p1] ];\n        }\n\n        function iterateKoch() {\n            if (iteration >= 8) { // Limit iterations for performance\n                 console.warn(\"Maximum iteration reached (8) to prevent performance issues.\");\n                 alert(\"Maximum iteration (8) reached. Further iterations might slow down your browser significantly.\");\n                 return;\n            }\n\n            const newSegments = [];\n            const sqrt3_2 = Math.sqrt(3) / 2;\n\n            for (const seg of segments) {\n                const p1 = seg[0];\n                const p2 = seg[1];\n\n                const dx = p2.x - p1.x;\n                const dy = p2.y - p1.y;\n\n                // Points dividing the segment into thirds\n                const pa = { x: p1.x + dx / 3, y: p1.y + dy / 3 };\n                const pb = { x: p1.x + 2 * dx / 3, y: p1.y + 2 * dy / 3 };\n\n                // Calculate the tip of the new triangle (pc)\n                // Midpoint between pa and pb\n                const midX = p1.x + dx / 2;\n                const midY = p1.y + dy / 2;\n                // Perpendicular vector scaled by sqrt(3)/2 * (segment length / 3)\n                const vecX = -dy * sqrt3_2 / 3;\n                const vecY = dx * sqrt3_2 / 3;\n\n                const pc = { x: midX + vecX, y: midY + vecY };\n\n                // Add the four new segments\n                newSegments.push([p1, pa]);\n                newSegments.push([pa, pc]);\n                newSegments.push([pc, pb]);\n                newSegments.push([pb, p2]);\n            }\n\n            segments = newSegments;\n            iteration++;\n            updateInfo();\n\n            // \"Animate\" by simply redrawing - instant change\n            // For a smooth visual transition, more complex animation logic (tweening) would be needed.\n            drawSnowflake();\n        }\n\n        // --- Drawing ---\n        function drawSnowflake() {\n            // Clear canvas with background color\n            ctx.fillStyle = backgroundColor;\n            ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n            // Save context state for transformations\n            ctx.save();\n\n            // Apply pan and zoom\n            // Translate origin to center for zooming, then apply pan, then scale\n            ctx.translate(canvas.width / 2, canvas.height / 2);\n            ctx.scale(zoomLevel, zoomLevel);\n            ctx.translate(-canvas.width / 2 + offsetX, -canvas.height / 2 + offsetY);\n\n\n            // Draw the snowflake shape\n            if (segments.length > 0) {\n                ctx.beginPath();\n                ctx.moveTo(segments[0][0].x, segments[0][0].y);\n                for (const seg of segments) {\n                    ctx.lineTo(seg[1].x, seg[1].y);\n                }\n                // No need to closePath explicitly if filling, but good practice\n                // ctx.closePath(); // Creates the final segment back to start\n\n                ctx.fillStyle = snowflakeColor;\n                ctx.fill();\n\n                // Optional: Add an outline\n                // ctx.strokeStyle = snowflakeColor; // Or a contrasting color\n                // ctx.lineWidth = 1 / zoomLevel; // Keep line width consistent visually\n                // ctx.stroke();\n            }\n\n            // Restore context state\n            ctx.restore();\n        }\n\n        // --- UI and State Updates ---\n        function updateInfo() {\n            iterationCountSpan.textContent = iteration;\n            const numSides = 3 * Math.pow(4, iteration);\n            // Use toLocaleString for better readability of large numbers\n            sideCountSpan.textContent = numSides.toLocaleString();\n        }\n\n        function reset() {\n            iteration = 0;\n            zoomLevel = 1.0;\n            offsetX = 0;\n            offsetY = 0;\n            segments = getInitialTriangle(canvas.width, canvas.height);\n            updateInfo();\n            drawSnowflake();\n        }\n\n        function applyColorScheme(schemeName) {\n            const scheme = colorSchemes[schemeName];\n            if (scheme) {\n                snowflakeColor = scheme.flake;\n                backgroundColor = scheme.bg;\n                canvasContainer.style.backgroundColor = backgroundColor; // Update container too\n                drawSnowflake();\n            }\n        }\n\n        // --- Event Listeners ---\n        iterateBtn.addEventListener('click', iterateKoch);\n        resetBtn.addEventListener('click', reset);\n        colorSchemeSelect.addEventListener('change', (e) => {\n            applyColorScheme(e.target.value);\n        });\n\n        // Zoom Listener\n        canvas.addEventListener('wheel', (event) => {\n            event.preventDefault(); // Prevent page scrolling\n\n            const rect = canvas.getBoundingClientRect();\n            const mouseX = event.clientX - rect.left;\n            const mouseY = event.clientY - rect.top;\n\n            // Calculate world coordinates before zoom\n            const worldXBefore = (mouseX - canvas.width / 2) / zoomLevel + canvas.width / 2 - offsetX;\n            const worldYBefore = (mouseY - canvas.height / 2) / zoomLevel + canvas.height / 2 - offsetY;\n\n            // Update zoom level\n            const zoomFactor = event.deltaY < 0 ? 1.1 : 1 / 1.1; // Zoom in or out\n            zoomLevel *= zoomFactor;\n            // Clamp zoom level if desired\n            // zoomLevel = Math.max(0.1, Math.min(zoomLevel, 20));\n\n             // Calculate world coordinates after zoom (if offset wasn't adjusted)\n            const worldXAfter = (mouseX - canvas.width / 2) / zoomLevel + canvas.width / 2 - offsetX;\n            const worldYAfter = (mouseY - canvas.height / 2) / zoomLevel + canvas.height / 2 - offsetY;\n\n            // Adjust offset to keep the point under the mouse stationary\n            offsetX += worldXAfter - worldXBefore;\n            offsetY += worldYAfter - worldYBefore;\n\n\n            drawSnowflake();\n        });\n\n        // Pan Listeners\n        canvas.addEventListener('mousedown', (event) => {\n            if (event.button === 0) { // Only pan with left mouse button\n                isPanning = true;\n                lastMouseX = event.clientX;\n                lastMouseY = event.clientY;\n                canvasContainer.style.cursor = 'grabbing'; // Change cursor\n            }\n        });\n\n        canvas.addEventListener('mousemove', (event) => {\n            if (isPanning) {\n                const dx = event.clientX - lastMouseX;\n                const dy = event.clientY - lastMouseY;\n\n                // Adjust offset based on mouse movement, scaled by zoom level\n                offsetX += dx / zoomLevel;\n                offsetY += dy / zoomLevel;\n\n                lastMouseX = event.clientX;\n                lastMouseY = event.clientY;\n\n                drawSnowflake();\n            }\n        });\n\n        // Stop panning on mouse up or mouse leave\n        canvas.addEventListener('mouseup', (event) => {\n            if (event.button === 0) {\n                isPanning = false;\n                canvasContainer.style.cursor = 'grab'; // Restore cursor\n            }\n        });\n        canvas.addEventListener('mouseleave', () => {\n             if (isPanning) { // Only reset if panning was active\n                 isPanning = false;\n                 canvasContainer.style.cursor = 'grab'; // Restore cursor\n             }\n        });\n\n\n        // --- Initial Setup ---\n        applyColorScheme(colorSchemeSelect.value); // Apply initial scheme\n        reset(); // Set initial state and draw\n\n    </script>\n\n</body>\n</html>"}, {"title": "Logic behind Chinese characters", "url": "https://youtu.be/U0EySK4T2aY?si=HV_ZHWS8KdJZmZJP", "spec": "Chinese characters are logograms, symbols that represent a word or phrase. Most modern-day characters are constructed of two components: a radical (which hints at the meaning of the character) and a sound component (which hints at its pronunciation). Radicals can appear in different positions: to the left, to the right, above, below, or even surrounding the rest of the character.\n\nBuild me an interactive web app to help a learner understand the structure of Chinese characters.\n\nSPECIFICATIONS:\n1. The app must feature a central display area for a Chinese character.\n2. The app must feature a palette containing a set of radicals (at least 5).\n3. The app must feature a palette containing a set of sound components (at least 5).\n4. The app must allow the user to drag radicals and/or sound components onto the central display area. When a radical and sound component are combined in this way, the resulting character should be displayed.\n5. The app must feature a short explanation of the radical component and the sound component of each character that is displayed. The short explanation should appear whenever a new character is displayed.\n6. When a new character is displayed, the app should also state the pronunciation of the character (using Pinyin).\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Chinese Character Builder</title>\n    <style>\n        body {\n            font-family: sans-serif;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            padding: 20px;\n            background-color: #f4f4f4;\n        }\n\n        h1 {\n            color: #333;\n            margin-bottom: 20px;\n        }\n\n        .container {\n            display: flex;\n            gap: 30px;\n            width: 100%;\n            max-width: 800px;\n            justify-content: center;\n            align-items: flex-start;\n            flex-wrap: wrap; /* Allow wrapping on smaller screens */\n        }\n\n        .palettes {\n            display: flex;\n            flex-direction: column;\n            gap: 20px;\n            flex-basis: 200px; /* Initial width for palettes */\n            flex-grow: 0;\n            flex-shrink: 0;\n        }\n\n        .palette {\n            border: 1px solid #ccc;\n            padding: 15px;\n            background-color: #fff;\n            border-radius: 8px;\n            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n        }\n\n        .palette h2 {\n            margin-top: 0;\n            margin-bottom: 10px;\n            font-size: 1.1em;\n            color: #555;\n            text-align: center;\n        }\n\n        .component {\n            display: inline-block;\n            border: 1px solid #ddd;\n            padding: 10px 15px;\n            margin: 5px;\n            font-size: 1.8em; /* Make components larger */\n            cursor: grab;\n            background-color: #e9e9e9;\n            border-radius: 4px;\n            user-select: none; /* Prevent text selection during drag */\n            transition: background-color 0.2s ease;\n        }\n\n        .component:hover {\n            background-color: #dcdcdc;\n        }\n\n        .component.dragging {\n            opacity: 0.5;\n            cursor: grabbing;\n        }\n\n        .display-section {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            flex-basis: 300px; /* Initial width for display */\n            flex-grow: 1;\n        }\n\n        #display-area {\n            width: 200px;\n            height: 200px;\n            border: 2px dashed #aaa;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            font-size: 6em; /* Large font for the character */\n            margin-bottom: 20px;\n            background-color: #fff;\n            border-radius: 8px;\n            position: relative; /* For positioning indicators */\n            transition: background-color 0.2s ease, border-color 0.2s ease;\n        }\n\n        #display-area.drag-over {\n            background-color: #e0f7ff;\n            border-color: #007bff;\n        }\n\n        .component-indicator {\n            position: absolute;\n            font-size: 0.3em; /* Smaller font size relative to display area */\n            color: #888;\n            background-color: rgba(255, 255, 255, 0.8);\n            padding: 2px 5px;\n            border-radius: 3px;\n            border: 1px solid #ccc;\n        }\n\n        #radical-indicator {\n            top: 5px;\n            left: 5px;\n        }\n\n        #phonetic-indicator {\n            top: 5px;\n            right: 5px;\n        }\n\n         #explanation-area {\n            width: 100%;\n            max-width: 300px; /* Match display area width */\n            padding: 15px;\n            border: 1px solid #ccc;\n            background-color: #f9f9f9;\n            border-radius: 8px;\n            min-height: 100px;\n            font-size: 0.9em;\n            color: #333;\n            box-shadow: 0 1px 3px rgba(0,0,0,0.05);\n        }\n\n        #explanation-area p {\n            margin: 5px 0;\n        }\n        #explanation-area strong {\n           color: #0056b3;\n        }\n\n        #character-display {\n            font-size: 1.5em; /* Make displayed character slightly larger here */\n            font-weight: bold;\n            color: #d9534f; /* Reddish color for emphasis */\n            margin-right: 10px;\n        }\n\n        #pinyin-display {\n            font-style: italic;\n            color: #5cb85c; /* Green color for pinyin */\n        }\n\n        #reset-button {\n            margin-top: 15px;\n            padding: 8px 15px;\n            font-size: 0.9em;\n            cursor: pointer;\n            background-color: #f0ad4e;\n            color: white;\n            border: none;\n            border-radius: 4px;\n            transition: background-color 0.2s ease;\n        }\n\n        #reset-button:hover {\n            background-color: #ec971f;\n        }\n\n        /* Responsive adjustments */\n        @media (max-width: 600px) {\n            .container {\n                flex-direction: column;\n                align-items: center;\n            }\n            .palettes {\n                flex-direction: row;\n                flex-wrap: wrap;\n                justify-content: center;\n                flex-basis: auto; /* Allow palettes to take full width */\n                width: 100%;\n            }\n            .palette {\n                flex-basis: calc(50% - 30px); /* Two palettes side-by-side */\n                min-width: 150px;\n            }\n             .display-section {\n                flex-basis: auto; /* Allow display to take full width */\n                width: 100%;\n                max-width: 300px; /* Limit max width */\n            }\n             #display-area {\n                 width: 180px;\n                 height: 180px;\n                 font-size: 5em;\n             }\n        }\n\n    </style>\n</head>\n<body>\n\n    <h1>Chinese Character Builder</h1>\n    <p>Drag a radical and a sound component to the central area to form a character.</p>\n\n    <div class=\"container\">\n\n        <div class=\"palettes\">\n            <div id=\"radical-palette\" class=\"palette\">\n                <h2>Radicals (Meaning Hint)</h2>\n                <!-- Radicals will be populated by script -->\n            </div>\n\n            <div id=\"phonetic-palette\" class=\"palette\">\n                <h2>Sound Components (Pronunciation Hint)</h2>\n                <!-- Phonetics will be populated by script -->\n            </div>\n        </div>\n\n        <div class=\"display-section\">\n            <div id=\"display-area\">\n                <!-- Indicators for dropped components -->\n                <div id=\"radical-indicator\" class=\"component-indicator\" style=\"display: none;\"></div>\n                <div id=\"phonetic-indicator\" class=\"component-indicator\" style=\"display: none;\"></div>\n                <!-- Character appears here -->\n            </div>\n             <button id=\"reset-button\">Reset</button>\n\n            <div id=\"explanation-area\">\n                <p>Drop components above...</p>\n                <!-- Explanation appears here -->\n            </div>\n        </div>\n\n    </div>\n\n    <script>\n        // --- Data ---\n        const radicals = ['女', '木', '氵', '口', '亻', '艹']; // Added one more radical\n        const phonetics = ['马', '青', '可', '也', '羊', '木']; // Added one more phonetic\n\n        const characterData = [\n            { character: \"妈\", radical: \"女\", phonetic: \"马\", pinyin: \"mā\", radicalMeaning: \"female\", phoneticMeaning: \"horse (provides 'ma' sound)\" },\n            { character: \"清\", radical: \"氵\", phonetic: \"青\", pinyin: \"qīng\", radicalMeaning: \"water\", phoneticMeaning: \"blue/green (provides 'qing' sound)\" },\n            { character: \"吗\", radical: \"口\", phonetic: \"马\", pinyin: \"ma\", radicalMeaning: \"mouth (often indicates question)\", phoneticMeaning: \"horse (provides 'ma' sound)\" },\n            { character: \"河\", radical: \"氵\", phonetic: \"可\", pinyin: \"hé\", radicalMeaning: \"water\", phoneticMeaning: \"'can/able' (provides 'ke/he' sound)\" },\n            { character: \"他\", radical: \"亻\", phonetic: \"也\", pinyin: \"tā\", radicalMeaning: \"person\", phoneticMeaning: \"'also' (provides 'ye/ta' sound)\" },\n            { character: \"样\", radical: \"木\", phonetic: \"羊\", pinyin: \"yàng\", radicalMeaning: \"wood/tree\", phoneticMeaning: \"sheep (provides 'yang' sound)\" },\n            { character: \"洋\", radical: \"氵\", phonetic: \"羊\", pinyin: \"yáng\", radicalMeaning: \"water\", phoneticMeaning: \"sheep (provides 'yang' sound)\" },\n            { character: \"沐\", radical: \"氵\", phonetic: \"木\", pinyin: \"mù\", radicalMeaning: \"water\", phoneticMeaning: \"tree/wood (provides 'mu' sound)\" },\n            { character: \"林\", radical: \"木\", phonetic: \"木\", pinyin: \"lín\", radicalMeaning: \"tree/wood\", phoneticMeaning: \"tree/wood (doubled implies forest)\" }, // Example using same component\n            { character: \"草\", radical: \"艹\", phonetic: \"早\", pinyin: \"cǎo\", radicalMeaning: \"grass\", phoneticMeaning: \"'early' (provides 'cao' sound - Note: phonetic 早 not in palette, example only)\" }, // Example showing phonetic not necessarily in palette\n             { character: \"菜\", radical: \"艹\", phonetic: \"采\", pinyin: \"cài\", radicalMeaning: \"grass (often vegetables)\", phoneticMeaning: \"'gather' (provides 'cai' sound - Note: phonetic 采 not in palette)\" } // Example showing phonetic not necessarily in palette\n        ];\n\n        // --- State ---\n        let currentRadical = null;\n        let currentPhonetic = null;\n\n        // --- DOM Elements ---\n        const radicalPalette = document.getElementById('radical-palette');\n        const phoneticPalette = document.getElementById('phonetic-palette');\n        const displayArea = document.getElementById('display-area');\n        const explanationArea = document.getElementById('explanation-area');\n        const resetButton = document.getElementById('reset-button');\n        const radicalIndicator = document.getElementById('radical-indicator');\n        const phoneticIndicator = document.getElementById('phonetic-indicator');\n\n        // --- Functions ---\n\n        // Populate palettes\n        function populatePalette(paletteElement, components, type) {\n            components.forEach(component => {\n                const div = document.createElement('div');\n                div.classList.add('component');\n                div.textContent = component;\n                div.draggable = true;\n                div.dataset.component = component;\n                div.dataset.type = type; // 'radical' or 'phonetic'\n                div.addEventListener('dragstart', handleDragStart);\n                div.addEventListener('dragend', handleDragEnd);\n                paletteElement.appendChild(div);\n            });\n        }\n\n        // Drag and Drop Handlers\n        function handleDragStart(event) {\n            event.target.classList.add('dragging');\n            const data = {\n                component: event.target.dataset.component,\n                type: event.target.dataset.type\n            };\n            event.dataTransfer.setData('application/json', JSON.stringify(data));\n            event.dataTransfer.effectAllowed = 'move';\n        }\n\n        function handleDragEnd(event) {\n            event.target.classList.remove('dragging');\n        }\n\n        function handleDragOver(event) {\n            event.preventDefault(); // Necessary to allow drop\n            displayArea.classList.add('drag-over');\n            event.dataTransfer.dropEffect = 'move';\n        }\n\n        function handleDragLeave(event) {\n            displayArea.classList.remove('drag-over');\n        }\n\n        function handleDrop(event) {\n            event.preventDefault();\n            displayArea.classList.remove('drag-over');\n            const data = JSON.parse(event.dataTransfer.getData('application/json'));\n\n            if (data.type === 'radical') {\n                currentRadical = data.component;\n                radicalIndicator.textContent = `Rad: ${currentRadical}`;\n                radicalIndicator.style.display = 'block';\n            } else if (data.type === 'phonetic') {\n                currentPhonetic = data.component;\n                phoneticIndicator.textContent = `Phon: ${currentPhonetic}`;\n                phoneticIndicator.style.display = 'block';\n            }\n\n            updateDisplay();\n        }\n\n        // Update Display and Explanation\n        function updateDisplay() {\n            // Clear previous content\n            displayArea.textContent = ''; // Clear character display first\n             // Re-add indicators which were cleared by setting textContent\n            displayArea.appendChild(radicalIndicator);\n            displayArea.appendChild(phoneticIndicator);\n            explanationArea.innerHTML = ''; // Clear explanation\n\n            if (currentRadical && currentPhonetic) {\n                const foundChar = characterData.find(item =>\n                    item.radical === currentRadical && item.phonetic === currentPhonetic\n                );\n\n                if (foundChar) {\n                    // Display combined character in the main area\n                    const charSpan = document.createElement('span');\n                    charSpan.textContent = foundChar.character;\n                    charSpan.style.fontSize = 'inherit'; // Inherit large size from displayArea\n                    displayArea.appendChild(charSpan);\n\n\n                    // Display explanation\n                    explanationArea.innerHTML = `\n                        <p>\n                           <span id=\"character-display\">${foundChar.character}</span>\n                           (<span id=\"pinyin-display\">${foundChar.pinyin}</span>)\n                        </p>\n                        <p><strong>Radical:</strong> ${foundChar.radical} (${foundChar.radicalMeaning})</p>\n                        <p><strong>Sound:</strong> ${foundChar.phonetic} (${foundChar.phoneticMeaning})</p>\n                    `;\n                } else {\n                     explanationArea.innerHTML = `<p>No standard character found for the combination: ${currentRadical} + ${currentPhonetic}. Try another pair!</p>`;\n                     // Keep indicators visible to show the failed combination attempt\n                }\n            } else {\n                 explanationArea.innerHTML = '<p>Drop components above...</p>';\n                 // Keep indicators visible if only one component is dropped\n            }\n        }\n\n        // Reset Function\n        function resetBuilder() {\n            currentRadical = null;\n            currentPhonetic = null;\n            radicalIndicator.style.display = 'none';\n            radicalIndicator.textContent = '';\n            phoneticIndicator.style.display = 'none';\n            phoneticIndicator.textContent = '';\n            updateDisplay(); // Update display to clear character and explanation\n        }\n\n        // --- Initialization ---\n        populatePalette(radicalPalette, radicals, 'radical');\n        populatePalette(phoneticPalette, phonetics, 'phonetic');\n\n        // Add drop zone event listeners\n        displayArea.addEventListener('dragover', handleDragOver);\n        displayArea.addEventListener('dragleave', handleDragLeave);\n        displayArea.addEventListener('drop', handleDrop);\n\n        // Add reset button listener\n        resetButton.addEventListener('click', resetBuilder);\n\n        // Initial explanation message\n        updateDisplay();\n\n    </script>\n\n</body>\n</html>"}, {"title": "Magical mitosis", "url": "https://www.youtube.com/watch?v=f-ldPgEfAHI", "spec": "Build me an interactive web app to help a learner understand the stages of mitosis, and their importance in growth and repair.\n\nSPECIFICATIONS:\n1.  The app should display a single cell. Initially, the cell should be in Interphase (pre-mitosis). The cell should be cartoonish and engaging, similar to the cells in the Amoeba Sisters' video.\n2. The user should be able to advance the cell through the different stages of mitosis (Prophase, Metaphase, Anaphase, Telophase) via a 'Next Stage' button.  Each time the button is pressed, the cell should visually transform to represent the next stage. A label should indicate the current stage.\n3.  At each stage, a brief (one sentence) description of what's happening within the cell should be displayed.  For example, during Metaphase, the description might read: 'Chromosomes line up in the middle of the cell.'\n4.  The app should clearly illustrate the chromosomes within the cell, and show how the chromosomes are duplicated during interphase, and then separated during mitosis\n5. Finally, upon reaching Telophase, the app should show that the original cell has divided into two identical daughter cells, each with a complete set of chromosomes. The text 'Identical Daughter Cells!' should be displayed.\n6. The UI should be simple and intuitive, focusing on the visual transformation of the cell through the stages. The visual design should be inviting and playful, encouraging exploration.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Interactive Mitosis Explorer</title>\n    <style>\n        body {\n            font-family: 'Comic Sans MS', 'Chalkboard SE', 'Marker Felt', sans-serif;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            min-height: 100vh;\n            background-color: #e0f7fa;\n            color: #004d40;\n            text-align: center;\n            overflow: hidden; /* Prevent scrollbars during transitions */\n        }\n\n        #app-container {\n            background-color: #ffffff;\n            padding: 30px;\n            border-radius: 20px;\n            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n            max-width: 600px;\n            width: 90%;\n        }\n\n        h1 {\n            color: #00796b;\n            margin-bottom: 10px;\n        }\n        h2 {\n            color: #00796b;\n            margin-top: 0;\n            margin-bottom: 15px;\n            font-size: 1.8em;\n            min-height: 1.8em; /* Prevent layout shifts */\n        }\n\n        #cell-visualization {\n            width: 100%;\n            max-width: 400px; /* Max size of SVG container */\n            height: 300px; /* Fixed height for SVG container */\n            margin: 20px auto;\n            background-color: #f0f0f0; /* Light background for contrast */\n            border-radius: 15px;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            overflow: hidden; /* Clip elements during transitions if needed */\n            border: 2px solid #b2dfdb;\n        }\n\n        #cell-svg {\n            width: 100%;\n            height: 100%;\n            display: block; /* Remove extra space below SVG */\n        }\n\n        /* Cell styles */\n        .cell-membrane {\n            fill: #ffebcd; /* Blanched Almond - cell color */\n            stroke: #d2b48c; /* Tan - cell outline */\n            stroke-width: 3;\n            transition: all 0.5s ease-in-out;\n        }\n        .nucleus-membrane {\n            fill: #add8e6; /* Light Blue - nucleus color */\n            stroke: #87ceeb; /* Sky Blue - nucleus outline */\n            stroke-width: 2;\n            transition: opacity 0.5s ease-in-out;\n        }\n\n        /* Chromosome styles */\n        .chromosome {\n            stroke-width: 5;\n            stroke-linecap: round;\n            transition: all 0.5s ease-in-out;\n        }\n        .chromo-pair1-stick1 { stroke: #ff6347; /* Tomato Red */ }\n        .chromo-pair1-stick2 { stroke: #ff6347; }\n        .chromo-pair2-stick1 { stroke: #4682b4; /* Steel Blue */ }\n        .chromo-pair2-stick2 { stroke: #4682b4; }\n\n        /* Spindle fibers */\n        .spindle-fiber {\n            stroke: #bdb76b; /* Dark Khaki */\n            stroke-width: 1;\n            stroke-dasharray: 3, 3;\n            transition: all 0.5s ease-in-out;\n        }\n\n        /* Centrioles / Poles */\n        .centriole {\n            fill: #b8860b; /* Dark Goldenrod */\n            transition: all 0.5s ease-in-out;\n        }\n\n\n        #stage-description {\n            margin-top: 15px;\n            font-size: 1.1em;\n            min-height: 3em; /* Reserve space to prevent jumps */\n            color: #004d40;\n        }\n\n        #next-button {\n            background-color: #00796b;\n            color: white;\n            border: none;\n            padding: 12px 25px;\n            border-radius: 25px;\n            font-size: 1.1em;\n            cursor: pointer;\n            transition: background-color 0.3s ease, transform 0.1s ease;\n            margin-top: 15px;\n            font-family: inherit; /* Use the same playful font */\n        }\n\n        #next-button:hover {\n            background-color: #004d40;\n        }\n        #next-button:active {\n            transform: scale(0.95);\n        }\n        #next-button:disabled {\n            background-color: #9e9e9e;\n            cursor: not-allowed;\n        }\n\n        #final-message {\n            color: #2e7d32; /* Dark Green */\n            font-weight: bold;\n            font-size: 1.5em;\n            margin-top: 20px;\n            display: none; /* Hidden initially */\n        }\n\n        /* Helper class for hiding elements */\n        .hidden {\n            display: none;\n            /* Using visibility might be better for transitions if elements should occupy space */\n            /* visibility: hidden;\n            opacity: 0; */\n        }\n\n    </style>\n</head>\n<body>\n\n    <div id=\"app-container\">\n        <h1>Magical Mitosis</h1>\n        <h2 id=\"stage-name\">Interphase</h2>\n\n        <div id=\"cell-visualization\">\n            <svg id=\"cell-svg\" viewBox=\"0 0 400 300\" preserveAspectRatio=\"xMidYMid meet\">\n                <!-- Define elements reusable across stages -->\n                <defs>\n                    <!-- Reusable chromosome parts (lines) -->\n                    <line id=\"c1s1\" class=\"chromosome chromo-pair1-stick1\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"20\" />\n                    <line id=\"c1s2\" class=\"chromosome chromo-pair1-stick2\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"20\" />\n                    <line id=\"c2s1\" class=\"chromosome chromo-pair2-stick1\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"30\" />\n                    <line id=\"c2s2\" class=\"chromosome chromo-pair2-stick2\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"30\" />\n                </defs>\n\n                <!-- Main Cell Membrane(s) -->\n                <ellipse id=\"cell-membrane-main\" class=\"cell-membrane\" cx=\"200\" cy=\"150\" rx=\"150\" ry=\"100\"/>\n                <ellipse id=\"cell-membrane-daughter1\" class=\"cell-membrane hidden\" cx=\"120\" cy=\"150\" rx=\"70\" ry=\"50\"/>\n                <ellipse id=\"cell-membrane-daughter2\" class=\"cell-membrane hidden\" cx=\"280\" cy=\"150\" rx=\"70\" ry=\"50\"/>\n\n                <!-- Nucleus Membrane -->\n                <ellipse id=\"nucleus-membrane\" class=\"nucleus-membrane\" cx=\"200\" cy=\"150\" rx=\"80\" ry=\"60\"/>\n\n                <!-- Chromosome Group -->\n                <g id=\"chromosomes-group\">\n                    <!-- These will be positioned by JS -->\n                    <!-- Pair 1 -->\n                    <use id=\"chromo1-stick1\" href=\"#c1s1\" x=\"180\" y=\"140\"/>\n                    <use id=\"chromo1-stick2\" href=\"#c1s2\" x=\"185\" y=\"140\" class=\"hidden\"/> <!-- Initially hidden duplicate -->\n                    <!-- Pair 2 -->\n                    <use id=\"chromo2-stick1\" href=\"#c2s1\" x=\"215\" y=\"135\"/>\n                    <use id=\"chromo2-stick2\" href=\"#c2s2\" x=\"220\" y=\"135\" class=\"hidden\"/> <!-- Initially hidden duplicate -->\n                </g>\n\n                <!-- Spindle Fibers Group -->\n                <g id=\"spindle-fibers-group\" class=\"hidden\">\n                    <!-- Lines will be added/updated by JS -->\n                </g>\n\n                <!-- Centrioles/Poles Group -->\n                <g id=\"poles-group\" class=\"hidden\">\n                    <circle id=\"pole-left\" class=\"centriole\" cx=\"50\" cy=\"150\" r=\"8\"/>\n                    <circle id=\"pole-right\" class=\"centriole\" cx=\"350\" cy=\"150\" r=\"8\"/>\n                </g>\n\n            </svg>\n        </div>\n\n        <p id=\"stage-description\">The cell grows, copies its DNA (chromosomes), and prepares for division.</p>\n        <button id=\"next-button\">Next Stage</button>\n        <p id=\"final-message\">Identical Daughter Cells!</p>\n    </div>\n\n    <script>\n        const stageNameEl = document.getElementById('stage-name');\n        const stageDescEl = document.getElementById('stage-description');\n        const nextButton = document.getElementById('next-button');\n        const finalMessageEl = document.getElementById('final-message');\n        const svg = document.getElementById('cell-svg');\n\n        // SVG Elements References\n        const cellMembraneMain = document.getElementById('cell-membrane-main');\n        const cellMembraneDaughter1 = document.getElementById('cell-membrane-daughter1');\n        const cellMembraneDaughter2 = document.getElementById('cell-membrane-daughter2');\n        const nucleusMembrane = document.getElementById('nucleus-membrane');\n        const chromosomesGroup = document.getElementById('chromosomes-group');\n        const spindleFibersGroup = document.getElementById('spindle-fibers-group');\n        const polesGroup = document.getElementById('poles-group');\n\n        // Chromosome elements\n        const chromo1Stick1 = document.getElementById('chromo1-stick1');\n        const chromo1Stick2 = document.getElementById('chromo1-stick2');\n        const chromo2Stick1 = document.getElementById('chromo2-stick1');\n        const chromo2Stick2 = document.getElementById('chromo2-stick2');\n\n        // --- Stage Definitions ---\n        const stages = [\n            {\n                name: \"Interphase\",\n                description: \"The cell grows, copies its DNA (chromosomes), and prepares for division.\",\n                action: drawInterphase\n            },\n            {\n                name: \"Prophase\",\n                description: \"Chromosomes condense and become visible, the nucleus disappears.\",\n                action: drawProphase\n            },\n            {\n                name: \"Metaphase\",\n                description: \"Chromosomes line up in the middle of the cell.\",\n                action: drawMetaphase\n            },\n            {\n                name: \"Anaphase\",\n                description: \"Sister chromatids separate and move to opposite ends of the cell.\",\n                action: drawAnaphase\n            },\n            {\n                name: \"Telophase\",\n                description: \"Chromosomes arrive at poles, new nuclei form, and the cell begins to divide.\",\n                action: drawTelophase\n            },\n            {\n                name: \"Cytokinesis\",\n                description: \"The cytoplasm divides, resulting in two identical daughter cells.\",\n                action: drawCytokinesis // This will show the final state\n            }\n        ];\n\n        let currentStageIndex = 0;\n\n        // --- Drawing Functions ---\n\n        function resetVisuals() {\n            // Hide elements that aren't always visible\n            nucleusMembrane.style.opacity = '1';\n            nucleusMembrane.classList.remove('hidden');\n            polesGroup.classList.add('hidden');\n            spindleFibersGroup.classList.add('hidden');\n            spindleFibersGroup.innerHTML = ''; // Clear old fibers\n            finalMessageEl.style.display = 'none';\n            cellMembraneMain.classList.remove('hidden');\n            cellMembraneMain.setAttribute('rx', 150); // Reset main cell shape\n            cellMembraneDaughter1.classList.add('hidden');\n            cellMembraneDaughter2.classList.add('hidden');\n\n            // Reset chromosome visibility and basic structure (single sticks)\n            chromo1Stick1.classList.remove('hidden');\n            chromo1Stick2.classList.add('hidden');\n            chromo2Stick1.classList.remove('hidden');\n            chromo2Stick2.classList.add('hidden');\n\n            // Reset transformations\n            chromo1Stick1.setAttribute('transform', '');\n            chromo1Stick2.setAttribute('transform', '');\n            chromo2Stick1.setAttribute('transform', '');\n            chromo2Stick2.setAttribute('transform', '');\n        }\n\n        function drawInterphase() {\n            resetVisuals();\n            // Position initial chromosomes loosely inside nucleus\n            chromo1Stick1.setAttribute('x', 180);\n            chromo1Stick1.setAttribute('y', 140);\n            chromo2Stick1.setAttribute('x', 215);\n            chromo2Stick1.setAttribute('y', 135);\n\n            // Simulate DNA duplication (chromosomes become Xs visually in Prophase)\n            // Here we just ensure the duplicates are hidden\n            chromo1Stick2.classList.add('hidden');\n            chromo2Stick2.classList.add('hidden');\n        }\n\n        function drawProphase() {\n            resetVisuals(); // Start clean slate except for inherited state\n            nucleusMembrane.style.opacity = '0'; // Fade out nucleus\n            polesGroup.classList.remove('hidden'); // Show poles\n\n            // Make chromosomes visible as 'X' shapes (sister chromatids joined)\n            // Position them condensed within the fading nucleus area\n            const xOffset = 5; // Offset for the second stick to form X\n\n            // Chromosome 1 (Red)\n            chromo1Stick1.setAttribute('x', 175);\n            chromo1Stick1.setAttribute('y', 130);\n            chromo1Stick1.setAttribute('transform', 'rotate(-30 175 140)'); // Rotate slightly\n            chromo1Stick2.classList.remove('hidden');\n            chromo1Stick2.setAttribute('x', 175 + xOffset); // Position near first stick\n            chromo1Stick2.setAttribute('y', 130);\n            chromo1Stick2.setAttribute('transform', 'rotate(30 180 140)'); // Rotate opposite way\n\n            // Chromosome 2 (Blue)\n            chromo2Stick1.setAttribute('x', 210);\n            chromo2Stick1.setAttribute('y', 145);\n            chromo2Stick1.setAttribute('transform', 'rotate(-30 210 160)');\n            chromo2Stick2.classList.remove('hidden');\n            chromo2Stick2.setAttribute('x', 210 + xOffset);\n            chromo2Stick2.setAttribute('y', 145);\n            chromo2Stick2.setAttribute('transform', 'rotate(30 215 160)');\n        }\n\n        function drawMetaphase() {\n            nucleusMembrane.style.opacity = '0'; // Keep nucleus hidden\n            polesGroup.classList.remove('hidden');\n            spindleFibersGroup.classList.remove('hidden');\n            spindleFibersGroup.innerHTML = ''; // Clear previous fibers\n\n            // Center X-shaped chromosomes on the metaphase plate (center line x=200)\n            const xOffset = 5;\n            const centerLineX = 200;\n            const poleLeftX = 50, poleRightX = 350, poleY = 150;\n\n            // Chromosome 1 (Red) - Position around y=130\n            const c1Y = 130;\n            chromo1Stick1.setAttribute('x', centerLineX - xOffset / 2);\n            chromo1Stick1.setAttribute('y', c1Y - 10); // Y adjusted for stick length\n            chromo1Stick1.setAttribute('transform', 'rotate(-30 200 130)');\n            chromo1Stick2.setAttribute('x', centerLineX - xOffset / 2);\n            chromo1Stick2.setAttribute('y', c1Y - 10);\n            chromo1Stick2.setAttribute('transform', 'rotate(30 200 130)');\n            addSpindleFiber(poleLeftX, poleY, centerLineX - xOffset / 2, c1Y);\n            addSpindleFiber(poleRightX, poleY, centerLineX + xOffset / 2, c1Y);\n\n\n            // Chromosome 2 (Blue) - Position around y=170\n            const c2Y = 170;\n            chromo2Stick1.setAttribute('x', centerLineX - xOffset / 2);\n            chromo2Stick1.setAttribute('y', c2Y - 15); // Y adjusted for stick length\n            chromo2Stick1.setAttribute('transform', 'rotate(-30 200 170)');\n            chromo2Stick2.setAttribute('x', centerLineX - xOffset / 2);\n            chromo2Stick2.setAttribute('y', c2Y - 15);\n            chromo2Stick2.setAttribute('transform', 'rotate(30 200 170)');\n            addSpindleFiber(poleLeftX, poleY, centerLineX - xOffset / 2, c2Y);\n            addSpindleFiber(poleRightX, poleY, centerLineX + xOffset / 2, c2Y);\n\n        }\n\n        function addSpindleFiber(x1, y1, x2, y2) {\n            const fiber = document.createElementNS('http://www.w3.org/2000/svg', 'line');\n            fiber.setAttribute('class', 'spindle-fiber');\n            fiber.setAttribute('x1', x1);\n            fiber.setAttribute('y1', y1);\n            fiber.setAttribute('x2', x2);\n            fiber.setAttribute('y2', y2);\n            spindleFibersGroup.appendChild(fiber);\n        }\n\n        function drawAnaphase() {\n            nucleusMembrane.style.opacity = '0';\n            polesGroup.classList.remove('hidden');\n            spindleFibersGroup.classList.remove('hidden');\n            spindleFibersGroup.innerHTML = ''; // Clear old fibers\n\n            // Separate sister chromatids and move them towards poles\n            const poleLeftX = 80; // Target X near left pole\n            const poleRightX = 320; // Target X near right pole\n            const poleY = 150; // Y coordinate of poles\n\n            // Chromosome 1 (Red) - sticks move apart\n            chromo1Stick1.setAttribute('x', poleLeftX);\n            chromo1Stick1.setAttribute('y', 140);\n            chromo1Stick1.setAttribute('transform', 'rotate(0)'); // Straighten\n            chromo1Stick2.setAttribute('x', poleRightX);\n            chromo1Stick2.setAttribute('y', 140);\n            chromo1Stick2.setAttribute('transform', 'rotate(0)'); // Straighten\n\n            // Chromosome 2 (Blue) - sticks move apart\n            chromo2Stick1.setAttribute('x', poleLeftX + 10); // Slightly offset\n            chromo2Stick1.setAttribute('y', 135);\n            chromo2Stick1.setAttribute('transform', 'rotate(0)'); // Straighten\n            chromo2Stick2.setAttribute('x', poleRightX - 10); // Slightly offset\n            chromo2Stick2.setAttribute('y', 135);\n            chromo2Stick2.setAttribute('transform', 'rotate(0)'); // Straighten\n\n            // Draw shorter spindle fibers pulling them\n            addSpindleFiber(50, poleY, poleLeftX, 150);\n            addSpindleFiber(50, poleY, poleLeftX + 10, 150);\n            addSpindleFiber(350, poleY, poleRightX, 150);\n            addSpindleFiber(350, poleY, poleRightX - 10, 150);\n\n            // Optional: Start pinching the cell membrane slightly\n            cellMembraneMain.setAttribute('rx', 145);\n            cellMembraneMain.setAttribute('ry', 95);\n        }\n\n        function drawTelophase() {\n            polesGroup.classList.add('hidden'); // Poles disappear\n            spindleFibersGroup.classList.add('hidden'); // Fibers disappear\n            spindleFibersGroup.innerHTML = '';\n\n            // Chromosomes arrive at poles (now single chromatids)\n            const poleLeftX = 80;\n            const poleRightX = 320;\n\n            // Position chromatids at poles (no longer X's)\n            chromo1Stick1.setAttribute('x', poleLeftX);\n            chromo1Stick1.setAttribute('y', 140);\n            chromo1Stick2.setAttribute('x', poleRightX); // This is now a separate chromosome\n            chromo1Stick2.setAttribute('y', 140);\n            chromo2Stick1.setAttribute('x', poleLeftX + 10);\n            chromo2Stick1.setAttribute('y', 135);\n            chromo2Stick2.setAttribute('x', poleRightX - 10); // This is now a separate chromosome\n            chromo2Stick2.setAttribute('y', 135);\n\n            // Hide the 'duplicate' stick for each pair? No, they *are* the chromosomes now.\n            // We need to conceptually treat them as full sets at each pole.\n\n            // Start forming new nuclei (visually represented by fading them in later or just pinching cell)\n            // Pinch the cell membrane significantly\n            cellMembraneMain.setAttribute('rx', 120); // Pinch more\n            cellMembraneMain.setAttribute('ry', 80); // Pinch more\n\n             // In a more complex viz, you'd draw faint nucleus outlines here\n        }\n\n        function drawCytokinesis() {\n            // Hide the main cell membrane\n             cellMembraneMain.classList.add('hidden');\n             nucleusMembrane.classList.add('hidden'); // Ensure original nucleus is gone\n             polesGroup.classList.add('hidden');\n             spindleFibersGroup.classList.add('hidden');\n\n            // Show the two daughter cells\n            cellMembraneDaughter1.classList.remove('hidden');\n            cellMembraneDaughter2.classList.remove('hidden');\n\n            // Position the chromosomes inside the daughter cells\n            // Daughter Cell 1 (Left)\n            const d1CenterX = 120;\n            const d1CenterY = 150;\n            chromo1Stick1.setAttribute('x', d1CenterX - 15);\n            chromo1Stick1.setAttribute('y', d1CenterY - 10);\n            chromo2Stick1.setAttribute('x', d1CenterX + 5);\n            chromo2Stick1.setAttribute('y', d1CenterY - 15);\n\n            // Daughter Cell 2 (Right)\n            const d2CenterX = 280;\n            const d2CenterY = 150;\n            chromo1Stick2.setAttribute('x', d2CenterX - 15); // Was at right pole\n            chromo1Stick2.setAttribute('y', d2CenterY - 10);\n            chromo2Stick2.setAttribute('x', d2CenterX + 5);  // Was at right pole\n            chromo2Stick2.setAttribute('y', d2CenterY - 15);\n\n            // Display final message and disable button\n            finalMessageEl.style.display = 'block';\n            nextButton.disabled = true;\n            nextButton.textContent = \"Finished!\";\n        }\n\n\n        // --- Event Listener ---\n        nextButton.addEventListener('click', () => {\n            currentStageIndex++;\n            if (currentStageIndex < stages.length) {\n                const currentStage = stages[currentStageIndex];\n                stageNameEl.textContent = currentStage.name;\n                stageDescEl.textContent = currentStage.description;\n                // Call the drawing function for the new stage\n                currentStage.action();\n            }\n             // Button disabling is handled within drawCytokinesis\n        });\n\n        // --- Initial Setup ---\n        function initialize() {\n            currentStageIndex = 0; // Start at Interphase\n            const initialStage = stages[currentStageIndex];\n            stageNameEl.textContent = initialStage.name;\n            stageDescEl.textContent = initialStage.description;\n            initialStage.action(); // Draw Interphase\n            nextButton.disabled = false; // Ensure button is enabled initially\n            nextButton.textContent = \"Next Stage\";\n            finalMessageEl.style.display = 'none'; // Hide final message\n        }\n\n        // Initialize the app when the page loads\n        initialize();\n\n    </script>\n\n</body>\n</html>"}, {"title": "History of Manhattan's Broadway", "url": "https://www.youtube.com/watch?v=erHe_WF4D1s", "spec": "Build me an interactive web app to help a learner understand the origins and defining characteristic of Broadway, a street in New York City. \n\nSPECIFICATIONS:\n1. The app should display a simplified map of Manhattan island. Start with a black and white base map showing the street grid.  The map must be zoomable and pannable.\n2. The app must allow the user to select a year (e.g., via a slider or dropdown menu).  The default year should be 2023.\n3. The app must show a highlighted (e.g., red) path of Broadway on the map. As the user changes the year, the highlighted path should change to reflect Broadway's route during that time period.\n    *   Initially, only the section of Broadway on the island of Manhattan should be displayed.  \n    *   For years before the Commissioners' Plan of 1811, Broadway will not conform to the street grid and will follow its natural, curving path.\n    *   For the first half of the 1800s, display only a part of Broadway leading up to The Common. \n    *   For later years, the path should extend to the northern tip of the island.\n4. Display text that changes dynamically based on the selected year.  This text should provide historical context about Broadway in that year, including key events and defining characteristics.\n5. The app should include a button to \"Show Highlights.\" When clicked, the map will display key locations along Broadway's path for the selected year. At least three such locations should be showcased (e.g., Trinity Church, City Hall Park, Columbus Circle)\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Interactive History of Broadway</title>\n    <meta charset=\"utf-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <link rel=\"stylesheet\" href=\"https://unpkg.com/leaflet@1.9.4/dist/leaflet.css\"\n     integrity=\"sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=\"\n     crossorigin=\"\"/>\n    <script src=\"https://unpkg.com/leaflet@1.9.4/dist/leaflet.js\"\n     integrity=\"sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=\"\n     crossorigin=\"\"></script>\n    <style>\n        html, body {\n            height: 100%;\n            margin: 0;\n            padding: 0;\n            font-family: sans-serif;\n            display: flex;\n            flex-direction: column; /* Stack elements vertically */\n        }\n        #map {\n            height: 65%; /* Adjust map height */\n            width: 100%;\n            background-color: #f0f0f0; /* Light grey background for contrast */\n        }\n        .controls {\n            padding: 10px;\n            background-color: #eee;\n            border-top: 1px solid #ccc;\n            display: flex;\n            align-items: center;\n            gap: 15px; /* Spacing between controls */\n        }\n        .info-panel {\n            padding: 15px;\n            background-color: #f9f9f9;\n            border-top: 1px solid #ccc;\n            flex-grow: 1; /* Take remaining vertical space */\n            overflow-y: auto; /* Allow scrolling if text is long */\n        }\n        label {\n            font-weight: bold;\n        }\n        #yearSlider {\n            flex-grow: 1; /* Slider takes available space */\n            max-width: 400px; /* Limit slider width */\n        }\n        #yearValue {\n            font-weight: bold;\n            min-width: 40px; /* Ensure space for year display */\n            text-align: right;\n        }\n        button {\n            padding: 8px 15px;\n            cursor: pointer;\n        }\n        /* Style for the Broadway path */\n        .broadway-path {\n            stroke: red;\n            stroke-width: 4;\n            fill: none; /* Make sure it's just a line */\n            opacity: 0.8;\n        }\n         /* Simple black and white tile layer filter */\n        .leaflet-tile-pane {\n            /* filter: grayscale(100%) contrast(120%); */ /* Simple B&W, slightly higher contrast */\n             filter: grayscale(100%) brightness(100%) contrast(100%); /* Alternative B&W */\n        }\n    </style>\n</head>\n<body>\n\n    <div id=\"map\"></div>\n    <div class=\"controls\">\n        <label for=\"yearSlider\">Year:</label>\n        <input type=\"range\" id=\"yearSlider\" min=\"1660\" max=\"2023\" value=\"2023\">\n        <span id=\"yearValue\">2023</span>\n        <button id=\"highlightButton\">Show Highlights</button>\n    </div>\n    <div class=\"info-panel\">\n        <h3>Broadway in <span id=\"infoYear\">2023</span></h3>\n        <p id=\"infoText\">Loading historical context...</p>\n    </div>\n\n<script>\n    // --- Configuration ---\n    const DEFAULT_YEAR = 2023;\n    const MIN_YEAR = 1660;\n    const MAX_YEAR = 2023;\n    const MAP_CENTER = [40.7580, -73.9855]; // Approx Times Square\n    const INITIAL_ZOOM = 12;\n    const MAX_BOUNDS_SW = [40.477399, -74.25909]; // Lower-left corner (approx Staten Island)\n    const MAX_BOUNDS_NE = [40.917577, -73.700272]; // Upper-right corner (approx Westchester County border)\n\n    // --- Data ---\n\n    // Simplified Broadway path data for different eras\n    // Coordinates are [Latitude, Longitude]\n    const broadwayPathData = [\n        {\n            maxYear: 1783, // Pre-Grid, Early Colonial / Post-Revolutionary\n            latLngs: [\n                [40.7033, -74.0137], // Bowling Green area\n                [40.7055, -74.0120], // Near Wall St\n                [40.7080, -74.0100], // Near Fulton St\n                [40.7115, -74.0075], // Approaching City Hall Park (The Common)\n                [40.7130, -74.0065]  // Ending near The Common\n            ],\n            text: \"Known as 'Heere Straat' under Dutch rule and later Broadway, this was the main thoroughfare of the small colonial city of New Amsterdam/New York. It followed a path originally created by Native Americans, winding north from the fort (near modern Bowling Green) up to 'The Common' (now City Hall Park). It was unpaved for much of its early history and lined with residences, taverns, and early churches like Trinity Church (founded 1697).\"\n        },\n        {\n            maxYear: 1850, // Post-Grid Plan, Early Expansion\n             latLngs: [\n                [40.7033, -74.0137], // Bowling Green\n                [40.7078, -74.0105], // Near Trinity Church\n                [40.7130, -74.0065], // City Hall Park\n                [40.7190, -74.0010], // Canal Street area\n                [40.7260, -73.9975], // Near Houston St (pre-grid alignment still noticeable)\n                [40.7330, -73.9920], // Near Astor Place / Grace Church\n                [40.7395, -73.9900]  // Approaching Union Square\n            ],\n            text: \"Following the Commissioners' Plan of 1811 which laid out Manhattan's grid, Broadway began its northward expansion but retained its diagonal path, cutting across the new grid. The section below City Hall Park remained the commercial heart. Hotels like the Astor House opened, and early theaters began appearing near The Park. It extended past Canal Street, becoming a fashionable shopping and residential street further north near Grace Church and Union Square, though still ending well below Midtown.\"\n        },\n        {\n            maxYear: 1900, // Gilded Age, Rise of Theater District\n            latLngs: [\n                [40.7033, -74.0137], // Bowling Green\n                [40.7078, -74.0105], // Trinity Church\n                [40.7130, -74.0065], // City Hall Park\n                [40.7260, -73.9975], // Houston St area\n                [40.7355, -73.9905], // Union Square\n                [40.7410, -73.9880], // Madison Square\n                [40.7505, -73.9860], // Herald Square (34th St)\n                [40.7570, -73.9855], // Times Square area (Longacre Square)\n                [40.7680, -73.9820], // Columbus Circle (59th St)\n                [40.7780, -73.9780], // Upper West Side begins\n                [40.7950, -73.9700], // Approx 96th St\n                [40.8100, -73.9620]  // Approx 125th St\n            ],\n            text: \"Broadway transformed dramatically. The southern end became the 'Canyon of Heroes.' The stretch from Union Square to Herald Square became the 'Ladies' Mile' shopping district. Crucially, the theatre district migrated north from Union Square, eventually centering around Longacre Square (renamed Times Square in 1904). Electric lights earned it the nickname 'The Great White Way'. Cable cars, then elevated trains, ran along parts of it. It extended significantly northward through the Upper West Side.\"\n        },\n        {\n            maxYear: 2023, // Modern Era\n            latLngs: [\n                [40.7033, -74.0137], // Bowling Green\n                [40.7078, -74.0105], // Trinity Church\n                [40.7130, -74.0065], // City Hall Park\n                [40.7260, -73.9975], // Houston St area\n                [40.7355, -73.9905], // Union Square\n                [40.7410, -73.9880], // Madison Square\n                [40.7505, -73.9860], // Herald Square\n                [40.7580, -73.9855], // Times Square\n                [40.7680, -73.9820], // Columbus Circle\n                [40.7780, -73.9780], // UWS ~72nd\n                [40.7950, -73.9700], // UWS ~96th\n                [40.8100, -73.9620], // UWS ~125th\n                [40.8250, -73.9530], // ~145th St\n                [40.8370, -73.9450], // ~160th St\n                [40.8500, -73.9380], // ~178th St (near GWB)\n                [40.8650, -73.9280], // Inwood / Dyckman St\n                [40.8735, -73.9210]  // Near northern tip (Ship Canal)\n            ],\n            text: \"Broadway remains a vital artery, extending the full length of Manhattan (and beyond, off the island). The Financial District dominates the south, SoHo and NoHo feature retail and lofts, Union Square is a hub, Midtown contains the world-famous Theatre District around Times Square, Lincoln Center hosts performing arts near Columbus Circle, and it continues through diverse residential neighborhoods of the Upper West Side, Washington Heights, and Inwood. Its diagonal path creates unique intersections like Times and Herald Squares.\"\n        }\n    ];\n\n    // Key highlight locations\n    const highlightLocations = [\n        { name: \"Bowling Green\", lat: 40.7038, lng: -74.0137, minYear: 1660 },\n        { name: \"Trinity Church\", lat: 40.7080, lng: -74.0118, minYear: 1697 },\n        { name: \"City Hall Park (The Common)\", lat: 40.7128, lng: -74.0060, minYear: 1700 },\n        { name: \"Grace Church\", lat: 40.7320, lng: -73.9915, minYear: 1846 },\n        { name: \"Union Square\", lat: 40.7359, lng: -73.9904, minYear: 1839 },\n        { name: \"Madison Square Park\", lat: 40.7419, lng: -73.9878, minYear: 1847 },\n        { name: \"Herald Square (34th St)\", lat: 40.7505, lng: -73.9863, minYear: 1890 },\n        { name: \"Times Square (Longacre Square until 1904)\", lat: 40.7580, lng: -73.9855, minYear: 1900 },\n        { name: \"Columbus Circle (59th St)\", lat: 40.7680, lng: -73.9820, minYear: 1905 },\n        { name: \"Lincoln Center\", lat: 40.7725, lng: -73.9835, minYear: 1962 }\n    ];\n\n    // --- Map Initialization ---\n    let map;\n    let broadwayLayer;\n    let highlightsLayer;\n    let highlightsVisible = false;\n\n    function initMap() {\n        map = L.map('map', {\n            center: MAP_CENTER,\n            zoom: INITIAL_ZOOM,\n            maxBounds: L.latLngBounds(MAX_BOUNDS_SW, MAX_BOUNDS_NE), // Restrict panning\n            minZoom: 11 // Prevent zooming out too far\n        });\n\n        // Add a black and white base map tile layer (CartoDB Positron No Labels)\n        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png', {\n            attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>',\n            subdomains: 'abcd',\n            maxZoom: 20\n        }).addTo(map);\n\n        // Initialize layers for Broadway path and highlights\n        broadwayLayer = L.polyline([], { className: 'broadway-path' }).addTo(map);\n        highlightsLayer = L.layerGroup().addTo(map); // Layer group to manage highlight markers\n\n        // Setup UI Elements\n        setupControls();\n\n        // Initial map update\n        updateMapAndText(DEFAULT_YEAR);\n    }\n\n    // --- UI Controls ---\n    const yearSlider = document.getElementById('yearSlider');\n    const yearValueDisplay = document.getElementById('yearValue');\n    const infoYearDisplay = document.getElementById('infoYear');\n    const infoTextDisplay = document.getElementById('infoText');\n    const highlightButton = document.getElementById('highlightButton');\n\n    function setupControls() {\n        yearSlider.min = MIN_YEAR;\n        yearSlider.max = MAX_YEAR;\n        yearSlider.value = DEFAULT_YEAR;\n        yearValueDisplay.textContent = DEFAULT_YEAR;\n\n        yearSlider.addEventListener('input', (event) => {\n            const year = parseInt(event.target.value, 10);\n            yearValueDisplay.textContent = year;\n            updateMapAndText(year);\n        });\n\n        highlightButton.addEventListener('click', toggleHighlights);\n    }\n\n    // --- Map and Text Update Logic ---\n\n    function getHistoricalData(year) {\n        // Find the first period whose maxYear is >= the selected year\n        for (const period of broadwayPathData) {\n            if (year <= period.maxYear) {\n                return period;\n            }\n        }\n        // Fallback to the last period if year is somehow out of range (shouldn't happen with slider)\n        return broadwayPathData[broadwayPathData.length - 1];\n    }\n\n    function updateMapAndText(year) {\n        const data = getHistoricalData(year);\n\n        // Update Broadway Path\n        broadwayLayer.setLatLngs(data.latLngs);\n\n        // Update Info Panel Text\n        infoYearDisplay.textContent = year;\n        infoTextDisplay.textContent = data.text;\n\n        // Update highlights if they are currently visible\n        if (highlightsVisible) {\n            showHighlights(year);\n        }\n    }\n\n    // --- Highlight Logic ---\n\n    function toggleHighlights() {\n        if (highlightsVisible) {\n            hideHighlights();\n        } else {\n            const currentYear = parseInt(yearSlider.value, 10);\n            showHighlights(currentYear);\n        }\n    }\n\n    function showHighlights(year) {\n        highlightsLayer.clearLayers(); // Remove existing markers\n\n        highlightLocations.forEach(loc => {\n            // Only show highlights relevant up to the selected year\n            if (year >= loc.minYear) {\n                const marker = L.marker([loc.lat, loc.lng]);\n                marker.bindTooltip(loc.name); // Show name on hover\n                highlightsLayer.addLayer(marker);\n            }\n        });\n\n        highlightsVisible = true;\n        highlightButton.textContent = \"Hide Highlights\";\n    }\n\n    function hideHighlights() {\n        highlightsLayer.clearLayers();\n        highlightsVisible = false;\n        highlightButton.textContent = \"Show Highlights\";\n    }\n\n    // --- Run Initialization ---\n    // Ensure the DOM is ready before initializing the map\n    document.addEventListener('DOMContentLoaded', initMap);\n\n</script>\n\n</body>\n</html>"}, {"title": "The craft of the casserole", "url": "https://www.youtube.com/watch?v=hfCwwG8Ats0", "spec": "Here's a spec for an interactive web app designed to complement a video about casserole recipes, focusing on the common steps and ingredients used in casserole preparation.\n\nSPECIFICATIONS:\n\n1.  **Core Mechanic: Ingredient Selection and Layering:** The user interface will consist of a blank casserole dish and a pantry shelf stocked with common casserole ingredients. The user's task is to drag and drop ingredients onto the dish in layers to create a complete casserole.  The user can select from a variety of ingredients\n\n2.  **Ingredient Categories:**  Categorize ingredients into:\n    *   Base (e.g., rice, pasta, polenta, potatoes)\n    *   Protein (e.g., ground beef, chicken, seafood)\n    *   Vegetables (e.g., green beans, onions, mushrooms, tomatoes)\n    *   Sauce (e.g., tomato sauce, enchilada sauce, cream of mushroom soup, béchamel)\n    *   Cheese (e.g., cheddar, mozzarella, Parmesan, goat cheese)\n    *   Topping (e.g., crushed potato chips, fried onions, bread crumbs)\n\n3.  **Layering Logic:** The app will enforce a logical layering sequence:\n    *   Base is the first layer.\n    *   Protein is the second layer\n    *   Vegetables are the third layer\n    *   Sauce is the fourth layer\n    *   Cheese is the fifth layer\n    *   Topping is the sixth and final layer.\n    If the user attempts to place an ingredient out of sequence, a friendly message should appear (e.g., \"You need a base layer first!\").\n\n4.  **Visual Feedback:** As the user layers ingredients, the casserole dish should visually update to reflect the selections.  Each ingredient should have a simple, recognizable visual representation.\n\n5.  **Completeness Indicator:** Once all layers have at least one ingredient selected, a \"Bake!\" button should activate. Clicking \"Bake!\" will display a celebratory message and a simple image of a finished casserole.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Interactive Casserole Builder</title>\n    <style>\n        body {\n            font-family: sans-serif;\n            display: flex;\n            justify-content: space-around;\n            align-items: flex-start;\n            padding: 20px;\n            background-color: #f4f4f4;\n            min-height: 100vh;\n            box-sizing: border-box;\n        }\n\n        #pantry {\n            width: 35%;\n            background-color: #e0cda9; /* Wood shelf color */\n            padding: 15px;\n            border-radius: 8px;\n            box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n            display: flex;\n            flex-direction: column;\n            gap: 15px; /* Space between categories */\n        }\n\n        .category {\n            border: 1px solid #b89d7c;\n            padding: 10px;\n            border-radius: 5px;\n            background-color: #fdf5e6; /* Lighter wood */\n        }\n\n        .category h3 {\n            margin: 0 0 10px 0;\n            font-size: 1em;\n            color: #6b4f34; /* Dark brown */\n            border-bottom: 1px solid #d3bfa8;\n            padding-bottom: 5px;\n            text-transform: uppercase;\n            letter-spacing: 1px;\n        }\n\n        .ingredient-list {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 8px;\n        }\n\n        .ingredient {\n            padding: 8px 12px;\n            background-color: #fff;\n            border: 1px solid #ccc;\n            border-radius: 15px; /* Pill shape */\n            cursor: grab;\n            user-select: none; /* Prevent text selection while dragging */\n            font-size: 0.9em;\n            transition: background-color 0.2s ease, box-shadow 0.2s ease;\n            text-align: center;\n        }\n\n        .ingredient:hover {\n            background-color: #eee;\n        }\n\n        .ingredient.dragging {\n            opacity: 0.5;\n            cursor: grabbing;\n            box-shadow: 0 4px 8px rgba(0,0,0,0.2);\n        }\n\n        #casserole-area {\n            width: 55%;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n        }\n\n        #casserole-dish {\n            width: 300px;\n            height: 200px;\n            border: 5px solid #8b4513; /* Brown border for dish */\n            background-color: #f5f5dc; /* Beige dish color */\n            border-radius: 10px 10px 30px 30px / 10px 10px 20px 20px; /* Casserole dish shape */\n            position: relative;\n            overflow: hidden; /* Hide overflowing layers */\n            box-shadow: inset 0 0 10px rgba(0,0,0,0.1), 0 5px 10px rgba(0,0,0,0.2);\n            margin-bottom: 15px;\n            display: flex;\n            flex-direction: column-reverse; /* Stack layers from bottom up */\n            justify-content: flex-start; /* Align layers at the bottom */\n        }\n\n        .layer {\n            width: 100%;\n            height: 0; /* Initially hidden */\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            font-size: 0.9em;\n            font-weight: bold;\n            color: rgba(0, 0, 0, 0.7);\n            text-align: center;\n            transition: height 0.4s ease-in-out, background-color 0.4s ease;\n            box-sizing: border-box;\n            border-top: 1px dashed rgba(0,0,0,0.1); /* Subtle layer separation */\n        }\n        .layer:first-child {\n            border-top: none;\n        }\n\n        /* Layer specific colors */\n        .layer[data-category=\"base\"] { background-color: #f0e68c; } /* Khaki - rice/pasta */\n        .layer[data-category=\"protein\"] { background-color: #d2b48c; } /* Tan - meat */\n        .layer[data-category=\"vegetables\"] { background-color: #90ee90; } /* Light green - veggies */\n        .layer[data-category=\"sauce\"] { background-color: #ff7f50; } /* Coral - sauce */\n        .layer[data-category=\"cheese\"] { background-color: #ffd700; } /* Gold - cheese */\n        .layer[data-category=\"topping\"] { background-color: #deb887; } /* Burlywood - topping */\n\n        #casserole-dish.drag-over {\n            border-color: #a0522d; /* Darker brown on drag over */\n            box-shadow: inset 0 0 15px rgba(0,0,0,0.2), 0 5px 10px rgba(0,0,0,0.2);\n        }\n\n        #message-area {\n            margin-top: 10px;\n            padding: 10px;\n            min-height: 40px; /* Reserve space */\n            width: 300px;\n            text-align: center;\n            font-weight: bold;\n            border-radius: 5px;\n        }\n\n        .message-error {\n            background-color: #ffdddd;\n            color: #d8000c;\n            border: 1px solid #d8000c;\n        }\n\n        .message-success {\n            background-color: #ddffdd;\n            color: #4f8a10;\n            border: 1px solid #4f8a10;\n        }\n\n        #bake-button {\n            padding: 12px 25px;\n            font-size: 1.1em;\n            background-color: #ff6347; /* Tomato color */\n            color: white;\n            border: none;\n            border-radius: 5px;\n            cursor: pointer;\n            transition: background-color 0.3s ease, opacity 0.3s ease;\n            margin-top: 15px;\n        }\n\n        #bake-button:disabled {\n            background-color: #cccccc;\n            cursor: not-allowed;\n            opacity: 0.6;\n        }\n\n        #bake-button:not(:disabled):hover {\n            background-color: #e5533d;\n        }\n\n        #result-area {\n            display: none; /* Hidden initially */\n            text-align: center;\n            margin-top: 20px;\n            padding: 20px;\n            background-color: #fff;\n            border-radius: 8px;\n            box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n        }\n\n        #result-area h2 {\n            color: #4CAF50; /* Green success color */\n        }\n\n        #result-area img {\n            max-width: 250px;\n            height: auto;\n            margin-top: 15px;\n            border-radius: 5px;\n            border: 3px solid #ccc;\n        }\n\n    </style>\n</head>\n<body>\n\n    <div id=\"pantry\">\n        <h2>Pantry</h2>\n        <div class=\"category\">\n            <h3>Base</h3>\n            <div class=\"ingredient-list\">\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"base\" data-name=\"Rice\">Rice</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"base\" data-name=\"Pasta\">Pasta</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"base\" data-name=\"Potatoes\">Potatoes</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"base\" data-name=\"Polenta\">Polenta</div>\n            </div>\n        </div>\n        <div class=\"category\">\n            <h3>Protein</h3>\n            <div class=\"ingredient-list\">\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"protein\" data-name=\"Ground Beef\">Ground Beef</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"protein\" data-name=\"Chicken\">Chicken</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"protein\" data-name=\"Tuna\">Tuna</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"protein\" data-name=\"Tofu\">Tofu</div>\n            </div>\n        </div>\n        <div class=\"category\">\n            <h3>Vegetables</h3>\n            <div class=\"ingredient-list\">\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"vegetables\" data-name=\"Green Beans\">Green Beans</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"vegetables\" data-name=\"Onions\">Onions</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"vegetables\" data-name=\"Mushrooms\">Mushrooms</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"vegetables\" data-name=\"Tomatoes\">Tomatoes</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"vegetables\" data-name=\"Broccoli\">Broccoli</div>\n            </div>\n        </div>\n        <div class=\"category\">\n            <h3>Sauce</h3>\n            <div class=\"ingredient-list\">\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"sauce\" data-name=\"Tomato Sauce\">Tomato Sauce</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"sauce\" data-name=\"Cream Soup\">Cream Soup</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"sauce\" data-name=\"Béchamel\">Béchamel</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"sauce\" data-name=\"Enchilada Sauce\">Enchilada Sauce</div>\n            </div>\n        </div>\n        <div class=\"category\">\n            <h3>Cheese</h3>\n            <div class=\"ingredient-list\">\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"cheese\" data-name=\"Cheddar\">Cheddar</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"cheese\" data-name=\"Mozzarella\">Mozzarella</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"cheese\" data-name=\"Parmesan\">Parmesan</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"cheese\" data-name=\"Goat Cheese\">Goat Cheese</div>\n            </div>\n        </div>\n         <div class=\"category\">\n            <h3>Topping</h3>\n            <div class=\"ingredient-list\">\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"topping\" data-name=\"Potato Chips\">Potato Chips</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"topping\" data-name=\"Fried Onions\">Fried Onions</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"topping\" data-name=\"Bread Crumbs\">Bread Crumbs</div>\n                <div class=\"ingredient\" draggable=\"true\" data-category=\"topping\" data-name=\"More Cheese\">More Cheese</div>\n            </div>\n        </div>\n    </div>\n\n    <div id=\"casserole-area\">\n        <h2>Build your casserole!</h2>\n        <div id=\"casserole-dish\">\n            <!-- Layers will be added here dynamically by JS -->\n            <div class=\"layer\" data-category=\"topping\"></div>\n            <div class=\"layer\" data-category=\"cheese\"></div>\n            <div class=\"layer\" data-category=\"sauce\"></div>\n            <div class=\"layer\" data-category=\"vegetables\"></div>\n            <div class=\"layer\" data-category=\"protein\"></div>\n            <div class=\"layer\" data-category=\"base\"></div>\n        </div>\n        <div id=\"message-area\">Drag ingredients to the dish in order!</div>\n        <button id=\"bake-button\" disabled>Bake!</button>\n        <div id=\"result-area\">\n            <h2>Casserole Baked!</h2>\n            <p>Delicious! You've successfully built your casserole.</p>\n            <!-- Simple placeholder image - replace with a real one if desired -->\n             <img src=\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEzMCIgdmlld0JveD0iMCAwIDIwMCAxMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8cmFkaWFsR3JhZGllbnQgaWQ9ImJnR3JhZGllbnQiIGN4PSI1MCUiIGN5PSI1MCUiIHI9IjcwJSIgZng9IjUwJSIgZnk9IjUwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNmZmU1YjMiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjZGVhMjU3Ii8+CiAgICA8L3JhZGlhbEdyYWRpZW50PgogIDwvZGVmcz4KICAgIDwhLS0gRGlzaCAtLT4KICAgIDxlbGxpcHNlIGN4PSIxMDAiIGN5PSIxMDUiIHJ4PSI5NSIgcnk9IjI1IiBmaWxsPSIjYTlhNWE0IiBzdHJva2U9IiM2OTY5NjkiIHN0cm9rZS13aWR0aD0iMiIvPgogICAgPHBhdGggZD0iTSA1IDgwIEEgOTUgMjUgMCAwIDEgMTk1IDgwIFYgMTA1IEEgOTUgMjUgMCAwIDEgNSAxMDUgWiIgZmlsbD0iI2E5YTVhNCIgc3Ryb2tlPSIjNjk2OTY5IiBzdHJva2Utd2lkdGg9IjIiLz4KICAgIDxlbGxpcHNlIGN4PSIxMDAiIGN5PSI4MCIgcng9Ijk1IiByeT0iMjUiIGZpbGw9IiNhOWE1YTQiIHN0cm9rZT0iIzY5Njk2OSIgc3Ryb2tlLXdpZHRoPSIyIi8+CiAgCiAgICA8IS0tIEZvb2QgLS0+CiAgICA8ZWxsaXBzZSBjeD0iMTAwIiBjeT0iODAiIHJ4PSI5MCIgcnk9IjIwIiBmaWxsPSJ1cmwoI2JnR3JhZGllbnQpIiAvPgogICAgCiAgICA8IS0tIEJ1YmJsZXMgLS0+CiAgICA8Y2lyY2xlIGN4PSI3MCIgY3k9IjcwIiByPSI1IiBmaWxsPSIjZmZmIiBvcGFjaXR5PSIwLjciLz4KICAgIDxjaXJjbGUgY3g9IjEyMCIgY3k9Ijc1IiByPSI3IiBmaWxsPSIjZmZmIiBvcGFjaXR5PSIwLjYiLz4KICAgIDxjaXJjbGUgY3g9IjE1MCIgY3k9Ijg1IiByPSI0IiBmaWxsPSIjZmZmIiBvcGFjaXR5PSIwLjgiLz4KICAgIDxjaXJjbGUgY3g9IjkwIiBjeT0iOTAiIHI9IjYiIGZpbGw9IiNmZmYiIG9wYWNpdHk9IjAuNiIvPgo8L3N2Zz4K\" alt=\"Baked Casserole\">\n        </div>\n    </div>\n\n    <script>\n        document.addEventListener('DOMContentLoaded', () => {\n            const ingredients = document.querySelectorAll('.ingredient');\n            const casseroleDish = document.getElementById('casserole-dish');\n            const layers = casseroleDish.querySelectorAll('.layer');\n            const messageArea = document.getElementById('message-area');\n            const bakeButton = document.getElementById('bake-button');\n            const resultArea = document.getElementById('result-area');\n            const pantry = document.getElementById('pantry');\n            const casseroleArea = document.getElementById('casserole-area');\n\n            const layerOrder = ['base', 'protein', 'vegetables', 'sauce', 'cheese', 'topping'];\n            const layerNames = {\n                base: 'Base',\n                protein: 'Protein',\n                vegetables: 'Vegetables',\n                sauce: 'Sauce',\n                cheese: 'Cheese',\n                topping: 'Topping'\n            };\n\n            let currentLayerIndex = 0;\n            let addedLayers = new Array(layerOrder.length).fill(false);\n            const layerHeightPercentage = 100 / layerOrder.length; // Distribute height equally\n\n            // --- Drag and Drop Event Handlers ---\n\n            ingredients.forEach(ingredient => {\n                ingredient.addEventListener('dragstart', (e) => {\n                    e.dataTransfer.setData('text/plain', JSON.stringify({\n                        category: ingredient.dataset.category,\n                        name: ingredient.dataset.name\n                    }));\n                    setTimeout(() => ingredient.classList.add('dragging'), 0); // Use timeout to avoid flickering\n                     messageArea.textContent = `Dragging ${ingredient.dataset.name}...`;\n                     messageArea.className = 'message-info'; // Use a neutral class or clear existing ones\n                });\n\n                ingredient.addEventListener('dragend', () => {\n                    ingredient.classList.remove('dragging');\n                     messageArea.textContent = `Add a ${layerNames[layerOrder[currentLayerIndex]]} layer next!`;\n                     messageArea.className = ''; // Clear message style\n                     // Restore error if needed\n                     if (messageArea.dataset.hasError === 'true') {\n                        displayMessage(`You still need a ${layerNames[layerOrder[currentLayerIndex]]} layer!`, true);\n                     }\n                });\n            });\n\n            casseroleDish.addEventListener('dragover', (e) => {\n                e.preventDefault(); // Necessary to allow dropping\n                casseroleDish.classList.add('drag-over');\n            });\n\n            casseroleDish.addEventListener('dragleave', () => {\n                casseroleDish.classList.remove('drag-over');\n            });\n\n            casseroleDish.addEventListener('drop', (e) => {\n                e.preventDefault();\n                casseroleDish.classList.remove('drag-over');\n                messageArea.dataset.hasError = 'false'; // Reset error flag\n\n                const data = JSON.parse(e.dataTransfer.getData('text/plain'));\n                const expectedCategory = layerOrder[currentLayerIndex];\n\n                if (data.category === expectedCategory) {\n                    // Correct layer\n                    const targetLayer = casseroleDish.querySelector(`.layer[data-category=\"${expectedCategory}\"]`);\n                    if (targetLayer && !addedLayers[currentLayerIndex]) {\n                        targetLayer.textContent = data.name;\n                        targetLayer.style.height = `${layerHeightPercentage}%`; // Make layer visible\n                        addedLayers[currentLayerIndex] = true;\n                        currentLayerIndex++;\n\n                        if (currentLayerIndex < layerOrder.length) {\n                             displayMessage(`Great! Now add a ${layerNames[layerOrder[currentLayerIndex]]} layer.`, false);\n                        } else {\n                             displayMessage('Casserole complete! Ready to bake.', false);\n                             bakeButton.disabled = false;\n                        }\n                    } else if (addedLayers[currentLayerIndex]) {\n                         displayMessage(`You already added a ${layerNames[expectedCategory]} layer. Try the next one!`, true);\n                         messageArea.dataset.hasError = 'true';\n                    }\n\n                } else {\n                    // Incorrect layer\n                    let requiredLayerName = layerNames[expectedCategory] || 'specific';\n                    displayMessage(`Oops! You need a ${requiredLayerName} layer first.`, true);\n                    messageArea.dataset.hasError = 'true';\n                }\n            });\n\n            // --- Bake Button Logic ---\n\n            bakeButton.addEventListener('click', () => {\n                // Hide the building interface\n                pantry.style.display = 'none';\n                casseroleArea.querySelector('h2').style.display = 'none'; // Hide \"Build Your Casserole!\"\n                casseroleDish.style.display = 'none';\n                messageArea.style.display = 'none';\n                bakeButton.style.display = 'none';\n\n                // Show the result\n                resultArea.style.display = 'block';\n            });\n\n             // --- Helper Function ---\n            function displayMessage(msg, isError) {\n                messageArea.textContent = msg;\n                messageArea.className = 'message-area'; // Reset classes\n                if (isError) {\n                    messageArea.classList.add('message-error');\n                } else {\n                     messageArea.classList.add('message-success');\n                }\n            }\n\n            // Initial message\n             displayMessage(`Start by adding a ${layerNames[layerOrder[0]]} layer.`, false);\n\n        });\n    </script>\n\n</body>\n</html>"}, {"title": "The craft of the cocktail", "url": "https://www.youtube.com/watch?v=AWnIqpsfyPU", "spec": "Build me an interactive web app to help a learner understand the common ratios that define families of cocktails. I want the user to be able to experiment with the ratios of different components in different cocktails. \n\nSPECIFICATIONS:\n\n1.  The app should present a list of at least 5 common cocktails (e.g., Old Fashioned, Manhattan, Daiquiri, Mojito, Margarita, Aperol Spritz, Martini, Vesper, Bamboo, etc.).\n2.  When the user selects a cocktail from the list, a visual representation of the cocktail is shown, and all ingredients are listed along with their ratio (e.g., 2oz spirit : 1oz sweet : 4 dashes bitters). The visual representation could be a simplified image of the drink in its glass, and this image should change dynamically based on the ratios that the user sets.\n3. Each ingredient should be represented by a slider. Moving the slider should change the amount of that ingredient, and the total volume of the drink should remain constant. As the user moves the slider, the ratio should update. Furthermore, moving the slider should change the image of the drink, and a message should appear describing how the change in ratio has affected the drink.\n4.  There should be a 'Reset' button to set the cocktail back to its original default ratios.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Cocktail Ratio Explorer</title>\n    <style>\n        body {\n            font-family: sans-serif;\n            display: flex;\n            padding: 20px;\n            gap: 30px;\n            background-color: #f4f4f4;\n            min-height: 95vh;\n        }\n\n        #cocktail-list {\n            list-style: none;\n            padding: 0;\n            margin: 0;\n            min-width: 150px;\n            background-color: #fff;\n            padding: 15px;\n            border-radius: 8px;\n            box-shadow: 0 2px 5px rgba(0,0,0,0.1);\n            height: fit-content;\n        }\n\n        #cocktail-list li {\n            padding: 8px 12px;\n            margin-bottom: 5px;\n            cursor: pointer;\n            border-radius: 4px;\n            transition: background-color 0.2s ease;\n        }\n\n        #cocktail-list li:hover {\n            background-color: #e0e0e0;\n        }\n\n        #cocktail-list li.active {\n            background-color: #007bff;\n            color: white;\n            font-weight: bold;\n        }\n\n        #cocktail-details {\n            flex-grow: 1;\n            display: flex;\n            flex-direction: column;\n            gap: 20px;\n            background-color: #fff;\n            padding: 20px;\n            border-radius: 8px;\n            box-shadow: 0 2px 5px rgba(0,0,0,0.1);\n        }\n\n        #cocktail-info {\n            display: flex;\n            gap: 30px;\n            align-items: flex-start;\n            min-height: 350px; /* Ensure space for visual */\n        }\n\n        #visual-area {\n            flex-basis: 200px; /* Fixed width for visual */\n            flex-shrink: 0;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            padding-top: 20px;\n        }\n\n        #cocktail-visual {\n            width: 120px; /* Glass width */\n            height: 250px; /* Glass height */\n            border: 3px solid #ccc;\n            border-top: none; /* Open top */\n            position: relative;\n            overflow: hidden; /* Clip layers */\n            background-color: #f0f8ff1a; /* Slight background tint */\n            margin-bottom: 10px; /* Space below glass */\n            display: flex;\n            flex-direction: column-reverse; /* Stack layers from bottom */\n            border-radius: 0 0 15px 15px; /* Rounded bottom */\n        }\n        /* Glass specific styles */\n        #cocktail-visual.rocks { width: 100px; height: 120px; border-radius: 0 0 8px 8px; }\n        #cocktail-visual.coupe { width: 140px; height: 150px; border: none; border-bottom: 3px solid #ccc; border-radius: 50% / 0 0 100% 100%; margin-top: 50px; /* Stem space */ }\n        #cocktail-visual.coupe::before { /* Stem */ content: ''; display: block; width: 4px; height: 50px; background: #ccc; margin: 0 auto; position: relative; top: 150px; }\n        #cocktail-visual.highball { width: 80px; height: 200px; border-radius: 0 0 5px 5px; }\n        #cocktail-visual.flute { width: 60px; height: 220px; border-radius: 0 0 5px 5px; }\n        #cocktail-visual.martini { width: 160px; height: 120px; border: none; border-bottom: 3px solid #ccc; border-radius: 100% 100% 0 0 / 100% 100% 0 0; transform: rotate(180deg); margin-top: 80px; }\n        #cocktail-visual.martini::before { /* Stem */ content: ''; display: block; width: 4px; height: 80px; background: #ccc; margin: 0 auto; position: relative; top: 120px; transform: rotate(180deg); }\n        #cocktail-visual.martini .ingredient-layer { transform: rotate(180deg); } /* Counter-rotate layers */\n\n\n        .ingredient-layer {\n            width: 100%;\n            transition: height 0.3s ease-in-out, background-color 0.3s ease;\n            flex-shrink: 0; /* Prevent layers from shrinking */\n        }\n\n        #ingredients-controls {\n            flex-grow: 1;\n        }\n\n        .ingredient-control {\n            margin-bottom: 15px;\n            display: flex;\n            align-items: center;\n            gap: 10px;\n        }\n\n        .ingredient-control label {\n            min-width: 180px; /* Align sliders */\n            display: inline-block;\n            font-size: 0.9em;\n        }\n        .ingredient-control label .ratio-value {\n            font-weight: bold;\n            display: inline-block;\n            min-width: 30px; /* Space for ratio number */\n            text-align: right;\n            margin-right: 5px;\n        }\n\n        .ingredient-control input[type=\"range\"] {\n            flex-grow: 1;\n            cursor: grab;\n            height: 8px;\n            border-radius: 4px;\n            background: #d3d3d3;\n            outline: none;\n            opacity: 0.7;\n            transition: opacity .2s;\n            appearance: none; /* Override default look */\n            -webkit-appearance: none;\n        }\n        .ingredient-control input[type=\"range\"]:hover {\n             opacity: 1;\n        }\n        /* Style the thumb */\n        .ingredient-control input[type=\"range\"]::-webkit-slider-thumb {\n            -webkit-appearance: none; /* Override default look */\n            appearance: none;\n            width: 18px; /* Set a specific slider handle width */\n            height: 18px; /* Slider handle height */\n            background: #007bff; /* Blue background */\n            cursor: pointer; /* Cursor on hover */\n            border-radius: 50%; /* Circular thumb */\n        }\n\n        .ingredient-control input[type=\"range\"]::-moz-range-thumb {\n            width: 18px; /* Set a specific slider handle width */\n            height: 18px; /* Slider handle height */\n            background: #007bff; /* Blue background */\n            cursor: pointer; /* Cursor on hover */\n            border-radius: 50%; /* Circular thumb */\n            border: none;\n        }\n\n\n        #feedback-message {\n            margin-top: 15px;\n            padding: 10px;\n            background-color: #e7f3ff;\n            border-left: 5px solid #007bff;\n            border-radius: 4px;\n            font-style: italic;\n            min-height: 40px; /* Prevent layout shifts */\n            color: #333;\n        }\n\n        #reset-button {\n            padding: 10px 15px;\n            background-color: #6c757d;\n            color: white;\n            border: none;\n            border-radius: 4px;\n            cursor: pointer;\n            transition: background-color 0.2s ease;\n            align-self: flex-start; /* Don't stretch button */\n        }\n\n        #reset-button:hover {\n            background-color: #5a6268;\n        }\n\n        /* Placeholder styling */\n        #cocktail-details.placeholder {\n             justify-content: center;\n             align-items: center;\n             color: #aaa;\n             font-size: 1.2em;\n        }\n\n    </style>\n</head>\n<body>\n\n    <ul id=\"cocktail-list\">\n        <!-- Cocktail names will be populated by JS -->\n    </ul>\n\n    <div id=\"cocktail-details\" class=\"placeholder\">\n        Select a cocktail to begin...\n        <!-- Content will be populated by JS -->\n    </div>\n\n    <script>\n        const cocktailsData = [\n            {\n                name: \"Old Fashioned\",\n                glass: \"rocks\",\n                ingredients: [\n                    { name: \"Bourbon/Rye\", defaultRatio: 4, unit: \"parts\", color: \"#a0522d\", type: \"spirit\" }, // ~2oz\n                    { name: \"Simple Syrup\", defaultRatio: 1, unit: \"part\", color: \"#f5f5dc\", type: \"sweet\" },    // ~0.5oz\n                    { name: \"Angostura Bitters\", defaultRatio: 0.5, unit: \"parts\", color: \"#8b0000\", type: \"bitter\" } // ~4 dashes (represented as ratio)\n                ],\n                description: \"A classic, spirit-forward cocktail balanced by sweetness and bitters.\"\n            },\n            {\n                name: \"Daiquiri\",\n                glass: \"coupe\",\n                ingredients: [\n                    { name: \"Light Rum\", defaultRatio: 4, unit: \"parts\", color: \"#f5f5f5\", type: \"spirit\" }, // ~2oz\n                    { name: \"Lime Juice\", defaultRatio: 2, unit: \"parts\", color: \"#90ee90\", type: \"sour\" },   // ~1oz\n                    { name: \"Simple Syrup\", defaultRatio: 1, unit: \"part\", color: \"#f5f5dc\", type: \"sweet\" }    // ~0.5oz\n                ],\n                description: \"The quintessential sour cocktail: bright, refreshing, and perfectly balanced.\"\n            },\n            {\n                name: \"Margarita\",\n                glass: \"coupe\", // Often rocks, but coupe is classic too\n                ingredients: [\n                    { name: \"Tequila\", defaultRatio: 4, unit: \"parts\", color: \"#fafad2\", type: \"spirit\" }, // ~2oz\n                    { name: \"Lime Juice\", defaultRatio: 2, unit: \"parts\", color: \"#90ee90\", type: \"sour\" },   // ~1oz\n                    { name: \"Triple Sec\", defaultRatio: 1, unit: \"part\", color: \"#ffcc99\", type: \"modifier\" } // ~0.5oz (acts as sweet+modifier)\n                ],\n                description: \"A vibrant mix of tequila, tart lime, and orange liqueur.\"\n            },\n            {\n                name: \"Manhattan\",\n                glass: \"coupe\",\n                ingredients: [\n                    { name: \"Rye Whiskey\", defaultRatio: 4, unit: \"parts\", color: \"#b8860b\", type: \"spirit\" },   // ~2oz\n                    { name: \"Sweet Vermouth\", defaultRatio: 2, unit: \"parts\", color: \"#dc143c\", type: \"modifier\" }, // ~1oz (sweet/modifier)\n                    { name: \"Angostura Bitters\", defaultRatio: 0.3, unit: \"parts\", color: \"#8b0000\", type: \"bitter\" }  // ~2 dashes\n                ],\n                description: \"A sophisticated and aromatic whiskey cocktail.\"\n            },\n            {\n                name: \"Negroni\",\n                glass: \"rocks\",\n                ingredients: [\n                    { name: \"Gin\", defaultRatio: 1, unit: \"part\", color: \"#e0ffff\", type: \"spirit\" },    // ~1oz\n                    { name: \"Campari\", defaultRatio: 1, unit: \"part\", color: \"#ff4500\", type: \"bitter\" },  // ~1oz (bitter/modifier)\n                    { name: \"Sweet Vermouth\", defaultRatio: 1, unit: \"part\", color: \"#dc143c\", type: \"modifier\" } // ~1oz (sweet/modifier)\n                ],\n                description: \"An iconic Italian aperitif, perfectly balanced between bitter, sweet, and botanical.\"\n            },\n             {\n                name: \"Aperol Spritz\",\n                glass: \"flute\", // Or large wine glass\n                ingredients: [\n                    { name: \"Prosecco\", defaultRatio: 3, unit: \"parts\", color: \"#fffacd\", type: \"lengthener\" }, // ~3oz\n                    { name: \"Aperol\", defaultRatio: 2, unit: \"parts\", color: \"#ff7f50\", type: \"bitter\" },   // ~2oz (bitter/sweet)\n                    { name: \"Soda Water\", defaultRatio: 1, unit: \"part\", color: \"#add8e6\", type: \"lengthener\" } // ~1oz\n                ],\n                description: \"A light, bubbly, and refreshing aperitif with a bittersweet orange profile.\"\n            },\n             {\n                name: \"Martini (Dry)\",\n                glass: \"martini\",\n                ingredients: [\n                    { name: \"Gin\", defaultRatio: 6, unit: \"parts\", color: \"#e0ffff\", type: \"spirit\" },       // ~3oz\n                    { name: \"Dry Vermouth\", defaultRatio: 1, unit: \"part\", color: \"#f0e68c\", type: \"modifier\" } // ~0.5oz\n                ],\n                description: \"The iconic clear cocktail, defined by its spirit (Gin or Vodka) and a hint of vermouth.\"\n            }\n        ];\n\n        const cocktailListEl = document.getElementById('cocktail-list');\n        const cocktailDetailsEl = document.getElementById('cocktail-details');\n        let currentCocktailIndex = -1;\n        let currentSliderValues = []; // To store current slider values for comparison\n\n        function init() {\n            populateCocktailList();\n            // Add listener to the list itself for delegation\n            cocktailListEl.addEventListener('click', (event) => {\n                if (event.target && event.target.nodeName === \"LI\") {\n                    const index = parseInt(event.target.dataset.index, 10);\n                    if (index !== currentCocktailIndex) {\n                        selectCocktail(index);\n                    }\n                }\n            });\n        }\n\n        function populateCocktailList() {\n            cocktailsData.forEach((cocktail, index) => {\n                const li = document.createElement('li');\n                li.textContent = cocktail.name;\n                li.dataset.index = index;\n                cocktailListEl.appendChild(li);\n            });\n        }\n\n        function selectCocktail(index) {\n            if (index < 0 || index >= cocktailsData.length) return;\n\n            currentCocktailIndex = index;\n            const cocktail = cocktailsData[index];\n\n            // Update active item in the list\n            const listItems = cocktailListEl.querySelectorAll('li');\n            listItems.forEach((li, i) => {\n                li.classList.toggle('active', i === index);\n            });\n\n            // Clear placeholder state\n            cocktailDetailsEl.classList.remove('placeholder');\n            cocktailDetailsEl.innerHTML = `\n                <h2>${cocktail.name}</h2>\n                <p>${cocktail.description || ''}</p>\n                <div id=\"cocktail-info\">\n                    <div id=\"visual-area\">\n                        <div id=\"cocktail-visual\" class=\"${cocktail.glass}\">\n                            <!-- Ingredient layers will be added here -->\n                        </div>\n                        <div id=\"current-ratio\">Ratio: Loading...</div>\n                    </div>\n                    <div id=\"ingredients-controls\">\n                        <!-- Sliders will be added here -->\n                    </div>\n                </div>\n                <div id=\"feedback-message\">Adjust the sliders to experiment with the balance.</div>\n                <button id=\"reset-button\">Reset to Default</button>\n            `;\n\n            const visualEl = document.getElementById('cocktail-visual');\n            const controlsEl = document.getElementById('ingredients-controls');\n            visualEl.innerHTML = ''; // Clear previous layers\n            controlsEl.innerHTML = ''; // Clear previous controls\n            currentSliderValues = []; // Reset current values\n\n            // Create layers and controls\n            cocktail.ingredients.forEach((ingredient, i) => {\n                // Visual Layer\n                const layer = document.createElement('div');\n                layer.classList.add('ingredient-layer');\n                layer.style.backgroundColor = ingredient.color;\n                layer.dataset.index = i;\n                visualEl.appendChild(layer);\n\n                // Control Slider\n                const controlDiv = document.createElement('div');\n                controlDiv.classList.add('ingredient-control');\n\n                const label = document.createElement('label');\n                label.setAttribute('for', `slider-${i}`);\n                // Display default unit initially, parts dynamically\n                const defaultPartDisplay = formatRatioPart(ingredient.defaultRatio);\n                label.innerHTML = `<span class=\"ratio-value\" id=\"ratio-value-${i}\">${defaultPartDisplay}</span> ${ingredient.name}`;\n\n                const slider = document.createElement('input');\n                slider.type = 'range';\n                slider.id = `slider-${i}`;\n                slider.dataset.index = i;\n                // Use a sensible range, e.g., 0 to double the max default ratio, or fixed like 0-10\n                const maxRatio = Math.max(...cocktail.ingredients.map(ing => ing.defaultRatio));\n                slider.min = 0;\n                // Ensure very small ratios like bitters are adjustable\n                slider.max = Math.max(10, Math.ceil(maxRatio * 1.5));\n                slider.step = 0.1; // Allow fine adjustments\n                slider.value = ingredient.defaultRatio;\n                currentSliderValues[i] = ingredient.defaultRatio; // Store initial value\n\n                slider.addEventListener('input', handleSliderInput);\n\n                controlDiv.appendChild(label);\n                controlDiv.appendChild(slider);\n                controlsEl.appendChild(controlDiv);\n            });\n\n            // Add Reset Button Listener\n            document.getElementById('reset-button').addEventListener('click', resetCocktail);\n\n            // Initial update\n            updateVisualAndRatios();\n        }\n\n        function handleSliderInput(event) {\n            const index = parseInt(event.target.dataset.index, 10);\n            const newValue = parseFloat(event.target.value);\n            currentSliderValues[index] = newValue; // Update the stored value\n            updateVisualAndRatios();\n        }\n\n        function updateVisualAndRatios() {\n            if (currentCocktailIndex < 0) return;\n\n            const cocktail = cocktailsData[currentCocktailIndex];\n            const visualEl = document.getElementById('cocktail-visual');\n            const layers = visualEl.querySelectorAll('.ingredient-layer');\n            const ratioDisplayEl = document.getElementById('current-ratio');\n            const feedbackEl = document.getElementById('feedback-message');\n\n            const totalValue = currentSliderValues.reduce((sum, value) => sum + value, 0);\n\n            let currentRatioString = [];\n            let hasChanged = false;\n            let changes = { spirit: 0, sweet: 0, sour: 0, bitter: 0, modifier: 0, lengthener: 0 };\n            const tolerance = 0.05; // Ignore tiny changes from float math\n\n            layers.forEach((layer, i) => {\n                const currentValue = currentSliderValues[i];\n                const percentage = totalValue > 0 ? (currentValue / totalValue) * 100 : 0;\n                layer.style.height = `${percentage}%`;\n\n                // Update label value\n                const ratioValueEl = document.getElementById(`ratio-value-${i}`);\n                if (ratioValueEl) {\n                    ratioValueEl.textContent = formatRatioPart(currentValue);\n                }\n                currentRatioString.push(formatRatioPart(currentValue));\n\n                // Check for changes from default for feedback\n                const defaultVal = cocktail.ingredients[i].defaultRatio;\n                const diff = currentValue - defaultVal;\n                if (Math.abs(diff) > tolerance) {\n                    hasChanged = true;\n                    const type = cocktail.ingredients[i].type;\n                    if (changes.hasOwnProperty(type)) {\n                        changes[type] += diff; // Accumulate changes by type\n                    }\n                }\n            });\n\n            ratioDisplayEl.textContent = `Ratio: ${currentRatioString.join(' : ')}`;\n\n            // Generate feedback message\n            feedbackEl.textContent = generateFeedbackMessage(cocktail, changes, hasChanged);\n        }\n\n        function formatRatioPart(value) {\n            // Show one decimal place if not an integer, otherwise show integer\n            return value % 1 === 0 ? value.toString() : value.toFixed(1);\n        }\n\n        function generateFeedbackMessage(cocktail, changes, hasChanged) {\n             if (!hasChanged) {\n                return \"This is the classic ratio. Try adjusting the sliders!\";\n            }\n\n            let messages = [];\n            const sortedChanges = Object.entries(changes)\n                .filter(([type, diff]) => Math.abs(diff) > 0.1) // Only consider significant changes\n                .sort(([, diffA], [, diffB]) => Math.abs(diffB) - Math.abs(diffA)); // Sort by magnitude\n\n            if (sortedChanges.length === 0) {\n                 return \"Minor adjustments made. The balance is close to the original.\";\n            }\n\n            // Describe the most significant change\n            const [topType, topDiff] = sortedChanges[0];\n            const direction = topDiff > 0 ? \"more\" : \"less\";\n            const adjective = getAdjective(topType, topDiff > 0);\n            if (adjective) {\n                messages.push(`The drink is now ${direction} ${adjective}.`);\n            } else {\n                 messages.push(`The amount of ${topType} has changed significantly.`);\n            }\n\n\n            // Optionally add secondary changes\n             if (sortedChanges.length > 1) {\n                const [secondType, secondDiff] = sortedChanges[1];\n                const secondDirection = secondDiff > 0 ? \"increased\" : \"decreased\";\n                if (Math.abs(secondDiff) > 0.5) { // Only mention if reasonably significant\n                   messages.push(`The proportion of ${secondType} has also ${secondDirection}.`);\n                }\n            }\n\n            // Check for potential imbalances\n            const totalValue = currentSliderValues.reduce((sum, value) => sum + value, 0);\n            const spiritProportion = currentSliderValues.reduce((sum, val, i) => sum + (cocktail.ingredients[i].type === 'spirit' ? val : 0), 0) / totalValue;\n            const sweetProportion = currentSliderValues.reduce((sum, val, i) => sum + (cocktail.ingredients[i].type === 'sweet' || (cocktail.ingredients[i].type === 'modifier' && changes.sweet === 0) ? val : 0), 0) / totalValue; // Include modifiers if no specific sweet\n            const sourProportion = currentSliderValues.reduce((sum, val, i) => sum + (cocktail.ingredients[i].type === 'sour' ? val : 0), 0) / totalValue;\n            const bitterProportion = currentSliderValues.reduce((sum, val, i) => sum + (cocktail.ingredients[i].type === 'bitter' ? val : 0), 0) / totalValue;\n\n            if (changes.sweet > 0.5 && changes.sour < -0.5 && sweetProportion > 0.3) messages.push(\"It might be leaning quite sweet now.\");\n            if (changes.sour > 0.5 && changes.sweet < -0.5 && sourProportion > 0.3) messages.push(\"It might be leaning quite tart now.\");\n            if (changes.spirit < -1 && spiritProportion < 0.3 && cocktail.ingredients.some(i => i.type === 'spirit')) messages.push(\"It's become less spirit-forward.\");\n            if (changes.bitter > 0.5 && bitterProportion > 0.15) messages.push(\"The bitterness is quite pronounced.\");\n            if (changes.lengthener > 1 && (spiritProportion + bitterProportion + sweetProportion + sourProportion) < 0.5) messages.push(\"It's significantly more diluted.\")\n\n\n            return messages.join(' ');\n        }\n\n        function getAdjective(type, isPositive) {\n             switch (type) {\n                case 'spirit': return 'spirit-forward'; // Less spirit-forward is harder to describe simply\n                case 'sweet': return isPositive ? 'sweet' : 'dry';\n                case 'sour': return isPositive ? 'tart' : 'mellow';\n                case 'bitter': return isPositive ? 'bitter' : 'smooth';\n                case 'modifier': return 'complex'; // Generic term for modifiers\n                case 'lengthener': return isPositive ? 'diluted' : 'concentrated';\n                default: return null;\n            }\n        }\n\n\n        function resetCocktail() {\n            if (currentCocktailIndex !== -1) {\n                // Reselecting the cocktail effectively resets it\n                selectCocktail(currentCocktailIndex);\n            }\n        }\n\n        // Initialize the app when the DOM is ready\n        document.addEventListener('DOMContentLoaded', init);\n\n    </script>\n\n</body>\n</html>"}, {"title": "Calligraphy & handlettering", "url": "https://www.youtube.com/watch?v=sBoVGqiSzr4", "spec": "Build an interactive web app to teach the fundamentals of modern calligraphy. The app should emphasize the contrast between thick downstrokes and thin upstrokes, and allow the user to practice creating this contrast.\n\nSPECIFICATIONS:\n\n1.  The app should display an interactive canvas or drawing area where the user can draw lines.\n2.  The app must have a control (e.g., a slider or button) that allows the user to adjust the \"pressure\" of the brush or pen.  This pressure will determine the thickness of the line being drawn.\n3.  The drawing area should include an optional grid overlay, mimicking the guidelines used in traditional calligraphy as seen in the video.\n4.  The app should provide an exercise where a user is shown a simple waveform (like a sine wave) and asked to trace it with the interactive brush, creating thick downstrokes and thin upstrokes based on the direction of movement.\n5.  The app must also allow the user to enter a word or phrase, and then trace the letters with appropriate thick and thin strokes, i.e., downstrokes will be thicker, upstrokes will be thinner\n6.  The app should include a reset button to clear the canvas and start over.\n7.  Provide visual feedback to the user when they successfully complete a stroke with the correct pressure (e.g., a color change or a small animation).\n8.  The app should feature an option to display X-height lines to help users maintain consistent letter sizing.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Calligraphy Practice Pad</title>\n    <style>\n        body {\n            font-family: sans-serif;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            padding: 20px;\n            background-color: #f4f4f4;\n            touch-action: none; /* Prevent default touch actions like scrolling */\n        }\n        #controls {\n            margin-bottom: 20px;\n            background-color: #fff;\n            padding: 15px;\n            border-radius: 8px;\n            box-shadow: 0 2px 5px rgba(0,0,0,0.1);\n            display: flex;\n            flex-wrap: wrap;\n            gap: 15px;\n            align-items: center;\n            justify-content: center;\n        }\n        #controls label, #controls button, #controls input[type=\"text\"] {\n            margin-right: 5px;\n        }\n        #controls > div {\n            display: flex;\n            align-items: center;\n        }\n        #canvas-container {\n            position: relative;\n            width: 800px; /* Adjust as needed */\n            height: 500px; /* Adjust as needed */\n            border: 1px solid #ccc;\n            background-color: #fff;\n            box-shadow: 0 2px 5px rgba(0,0,0,0.1);\n            cursor: crosshair;\n            overflow: hidden; /* Hide any overflow */\n        }\n        canvas {\n            display: block;\n            position: absolute;\n            top: 0;\n            left: 0;\n        }\n        #bgCanvas {\n            z-index: 0;\n        }\n        #drawingCanvas {\n            z-index: 1;\n        }\n        button {\n            padding: 8px 15px;\n            border: none;\n            background-color: #007bff;\n            color: white;\n            border-radius: 4px;\n            cursor: pointer;\n            transition: background-color 0.2s;\n        }\n        button:hover {\n            background-color: #0056b3;\n        }\n        input[type=\"range\"] {\n            cursor: pointer;\n            vertical-align: middle;\n        }\n        input[type=\"text\"] {\n            padding: 8px;\n            border: 1px solid #ccc;\n            border-radius: 4px;\n        }\n        .control-group {\n            border: 1px solid #eee;\n            padding: 10px;\n            border-radius: 5px;\n        }\n        #thicknessValue {\n            font-weight: bold;\n            min-width: 20px;\n            display: inline-block;\n            text-align: right;\n        }\n    </style>\n</head>\n<body>\n\n    <h1>Modern Calligraphy Practice</h1>\n    <p>Practice thick downstrokes and thin upstrokes.</p>\n\n    <div id=\"controls\">\n        <div class=\"control-group\">\n            <label for=\"thicknessSlider\">Max Thickness:</label>\n            <input type=\"range\" id=\"thicknessSlider\" min=\"2\" max=\"40\" value=\"15\">\n            <span id=\"thicknessValue\">15</span>px\n        </div>\n\n        <div class=\"control-group\">\n            <label>Guides:</label>\n            <input type=\"checkbox\" id=\"gridToggle\">\n            <label for=\"gridToggle\">Grid</label>\n            <input type=\"checkbox\" id=\"xHeightToggle\">\n            <label for=\"xHeightToggle\">X-Height</label>\n        </div>\n\n        <div class=\"control-group\">\n            <label>Exercises:</label>\n            <button id=\"waveformBtn\">Waveform Trace</button>\n            <input type=\"text\" id=\"textInput\" placeholder=\"Enter text to trace\">\n            <button id=\"textTraceBtn\">Trace Text</button>\n        </div>\n\n        <button id=\"resetBtn\">Reset Canvas</button>\n    </div>\n\n    <div id=\"canvas-container\">\n        <canvas id=\"bgCanvas\" width=\"800\" height=\"500\"></canvas>\n        <canvas id=\"drawingCanvas\" width=\"800\" height=\"500\"></canvas>\n    </div>\n\n    <script>\n        document.addEventListener('DOMContentLoaded', () => {\n            const bgCanvas = document.getElementById('bgCanvas');\n            const drawingCanvas = document.getElementById('drawingCanvas');\n            const bgCtx = bgCanvas.getContext('2d');\n            const drawCtx = drawingCanvas.getContext('2d');\n            const controls = document.getElementById('controls');\n            const thicknessSlider = document.getElementById('thicknessSlider');\n            const thicknessValue = document.getElementById('thicknessValue');\n            const gridToggle = document.getElementById('gridToggle');\n            const xHeightToggle = document.getElementById('xHeightToggle');\n            const resetBtn = document.getElementById('resetBtn');\n            const waveformBtn = document.getElementById('waveformBtn');\n            const textInput = document.getElementById('textInput');\n            const textTraceBtn = document.getElementById('textTraceBtn');\n\n            let isDrawing = false;\n            let lastX = 0;\n            let lastY = 0;\n            let maxThickness = parseInt(thicknessSlider.value, 10);\n            const minThickness = 1; // Fixed minimum thickness for upstrokes\n            const upstrokeColor = '#fd7e14'; // Orange for thin upstrokes (feedback)\n            const downstrokeColor = '#0d6efd'; // Blue for thick downstrokes (feedback)\n\n            // --- Canvas Setup ---\n            function resizeCanvas() {\n                const container = document.getElementById('canvas-container');\n                // Use fixed size as set in HTML/CSS\n                bgCanvas.width = drawingCanvas.width = 800;\n                bgCanvas.height = drawingCanvas.height = 500;\n                drawCtx.lineCap = 'round';\n                drawCtx.lineJoin = 'round';\n                clearBackground(); // Redraw background guides on resize\n                // Note: User drawings are lost on resize in this simple setup\n            }\n\n            // --- Drawing Logic ---\n            function getMousePos(canvas, evt) {\n                const rect = canvas.getBoundingClientRect();\n                let clientX, clientY;\n                if (evt.touches && evt.touches.length > 0) {\n                    clientX = evt.touches[0].clientX;\n                    clientY = evt.touches[0].clientY;\n                } else {\n                    clientX = evt.clientX;\n                    clientY = evt.clientY;\n                }\n                return {\n                    x: clientX - rect.left,\n                    y: clientY - rect.top\n                };\n            }\n\n            function startDrawing(e) {\n                e.preventDefault(); // Prevent scrolling on touch\n                isDrawing = true;\n                const pos = getMousePos(drawingCanvas, e);\n                [lastX, lastY] = [pos.x, pos.y];\n                // Optional: Draw a starting dot\n                // drawCtx.beginPath();\n                // drawCtx.fillStyle = downstrokeColor; // Start assuming down? Or use a neutral color?\n                // drawCtx.arc(lastX, lastY, maxThickness / 4, 0, Math.PI * 2);\n                // drawCtx.fill();\n            }\n\n            function draw(e) {\n                if (!isDrawing) return;\n                e.preventDefault();\n\n                const pos = getMousePos(drawingCanvas, e);\n                const currentX = pos.x;\n                const currentY = pos.y;\n\n                const dy = currentY - lastY;\n                // const dx = currentX - lastX; // Could be used for angle calculation if needed\n\n                let targetThickness;\n                let strokeColor;\n\n                // Determine thickness and color based on vertical direction\n                if (dy > 0.1) { // Moving Down (allow small tolerance)\n                    targetThickness = maxThickness;\n                    strokeColor = downstrokeColor;\n                } else if (dy < -0.1) { // Moving Up\n                    targetThickness = minThickness;\n                    strokeColor = upstrokeColor;\n                } else { // Horizontal or very small movement - use thin stroke\n                    targetThickness = minThickness;\n                    strokeColor = upstrokeColor; // Or a neutral color like '#888'\n                }\n\n                 // Smooth thickness transition (optional, adds complexity)\n                // let currentLineWidth = parseFloat(drawCtx.lineWidth);\n                // targetThickness = currentLineWidth + (targetThickness - currentLineWidth) * 0.3; // Simple interpolation\n\n\n                drawCtx.lineWidth = targetThickness;\n                drawCtx.strokeStyle = strokeColor;\n\n                drawCtx.beginPath();\n                drawCtx.moveTo(lastX, lastY);\n                drawCtx.lineTo(currentX, currentY);\n                drawCtx.stroke();\n\n                [lastX, lastY] = [currentX, currentY];\n            }\n\n            function stopDrawing(e) {\n                if (isDrawing) {\n                     e.preventDefault();\n                     isDrawing = false;\n                     // Optional: Smooth the end of the line if needed\n                }\n            }\n\n            // --- Guide Drawing ---\n            function clearBackground() {\n                bgCtx.clearRect(0, 0, bgCanvas.width, bgCanvas.height);\n                drawBackgroundGuides();\n            }\n\n            function drawBackgroundGuides() {\n                 bgCtx.clearRect(0, 0, bgCanvas.width, bgCanvas.height); // Clear previous guides\n                 if (gridToggle.checked) drawGrid();\n                 if (xHeightToggle.checked) drawXHeightLines();\n                 // Draw active exercise guide if any\n                 if (document.body.dataset.exercise === 'wave') drawWaveformGuide();\n                 if (document.body.dataset.exercise === 'text' && document.body.dataset.text) drawTextGuide(document.body.dataset.text);\n            }\n\n            function drawGrid() {\n                const gridSize = 20;\n                const slantAngle = -15 * Math.PI / 180; // Approx 55 deg from horizontal is common, let's use -15 deg from vertical\n                const tanSlant = Math.tan(slantAngle);\n\n                bgCtx.strokeStyle = '#e0e0e0'; // Light gray for grid\n                bgCtx.lineWidth = 0.5;\n\n                // Horizontal lines\n                for (let y = gridSize; y < bgCanvas.height; y += gridSize) {\n                    bgCtx.beginPath();\n                    bgCtx.moveTo(0, y);\n                    bgCtx.lineTo(bgCanvas.width, y);\n                    bgCtx.stroke();\n                }\n                // Vertical lines\n                for (let x = gridSize; x < bgCanvas.width; x += gridSize) {\n                    bgCtx.beginPath();\n                    bgCtx.moveTo(x, 0);\n                    bgCtx.lineTo(x, bgCanvas.height);\n                    bgCtx.stroke();\n                }\n\n                // Slant lines (typical for calligraphy)\n                 bgCtx.strokeStyle = '#d0d0f0'; // Lighter blue for slant\n                 for (let x = -bgCanvas.height * tanSlant; x < bgCanvas.width; x += gridSize * 1.5) {\n                     bgCtx.beginPath();\n                     bgCtx.moveTo(x, 0);\n                     // Calculate endpoint: y = H, x = startX + H*tan(slant)\n                     // Need start point where line intersects y=0 or x=0/W\n                     // Line eq: Y = tan(slant) * (X - X0) -> X = X0 + Y/tan(slant)\n                     // Start at (x, 0), end at (x + H*tanSlant, H)\n                     bgCtx.lineTo(x + bgCanvas.height * tanSlant, bgCanvas.height);\n                     bgCtx.stroke();\n                 }\n            }\n\n            function drawXHeightLines() {\n                const baseLine = bgCanvas.height * 0.65;\n                const xHeight = 40; // Example size\n                const ascenderHeight = 60;\n                const descenderHeight = 40;\n\n                bgCtx.strokeStyle = '#f8cdda'; // Pinkish for x-height guides\n                bgCtx.lineWidth = 1;\n                bgCtx.setLineDash([5, 5]); // Dashed lines\n\n                // Baseline\n                bgCtx.beginPath();\n                bgCtx.moveTo(0, baseLine);\n                bgCtx.lineTo(bgCanvas.width, baseLine);\n                bgCtx.stroke();\n\n                // X-height line\n                bgCtx.beginPath();\n                bgCtx.moveTo(0, baseLine - xHeight);\n                bgCtx.lineTo(bgCanvas.width, baseLine - xHeight);\n                bgCtx.stroke();\n\n                // Ascender line\n                bgCtx.beginPath();\n                bgCtx.moveTo(0, baseLine - xHeight - ascenderHeight);\n                bgCtx.lineTo(bgCanvas.width, baseLine - xHeight - ascenderHeight);\n                bgCtx.stroke();\n\n                // Descender line\n                 bgCtx.beginPath();\n                 bgCtx.moveTo(0, baseLine + descenderHeight);\n                 bgCtx.lineTo(bgCanvas.width, baseLine + descenderHeight);\n                 bgCtx.stroke();\n\n                bgCtx.setLineDash([]); // Reset dashes\n            }\n\n            // --- Exercise Guides ---\n            function drawWaveformGuide() {\n                const amplitude = 50;\n                const frequency = 0.03;\n                const yOffset = bgCanvas.height / 2;\n\n                bgCtx.strokeStyle = '#a0e0a0'; // Light green for guide\n                bgCtx.lineWidth = 1;\n                bgCtx.setLineDash([3, 3]);\n\n                bgCtx.beginPath();\n                bgCtx.moveTo(0, yOffset);\n                for (let x = 0; x < bgCanvas.width; x++) {\n                    const y = yOffset + amplitude * Math.sin(x * frequency);\n                    bgCtx.lineTo(x, y);\n                }\n                bgCtx.stroke();\n                bgCtx.setLineDash([]);\n            }\n\n            function drawTextGuide(text) {\n                const fontSize = 100; // Adjust as needed\n                const xPos = 50;\n                const yPos = bgCanvas.height * 0.65; // Align with baseline if x-height is on\n\n                bgCtx.font = `${fontSize}px cursive`; // Choose a suitable cursive font if available\n                bgCtx.fillStyle = '#c0c0c0'; // Light gray for text guide\n                bgCtx.textAlign = 'left';\n                bgCtx.textBaseline = 'alphabetic'; // Align text bottom to yPos (like baseline)\n\n                bgCtx.fillText(text, xPos, yPos);\n            }\n\n            // --- Event Listeners ---\n            thicknessSlider.addEventListener('input', (e) => {\n                maxThickness = parseInt(e.target.value, 10);\n                thicknessValue.textContent = maxThickness;\n            });\n\n            gridToggle.addEventListener('change', clearBackground);\n            xHeightToggle.addEventListener('change', clearBackground);\n\n            resetBtn.addEventListener('click', () => {\n                drawCtx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height);\n                // Optionally reset exercise mode\n                document.body.dataset.exercise = 'free';\n                document.body.dataset.text = '';\n                clearBackground(); // Redraw guides\n            });\n\n            waveformBtn.addEventListener('click', () => {\n                drawCtx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height); // Clear user drawing\n                document.body.dataset.exercise = 'wave';\n                document.body.dataset.text = '';\n                clearBackground(); // Redraw background with wave guide\n            });\n\n            textTraceBtn.addEventListener('click', () => {\n                const text = textInput.value.trim();\n                if (text) {\n                    drawCtx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height); // Clear user drawing\n                    document.body.dataset.exercise = 'text';\n                    document.body.dataset.text = text;\n                    clearBackground(); // Redraw background with text guide\n                } else {\n                     alert(\"Please enter some text to trace.\");\n                     // Optionally clear previous text guide if input is now empty\n                     if (document.body.dataset.exercise === 'text') {\n                         document.body.dataset.exercise = 'free';\n                         document.body.dataset.text = '';\n                         clearBackground();\n                     }\n                }\n            });\n\n            // Mouse events\n            drawingCanvas.addEventListener('mousedown', startDrawing);\n            drawingCanvas.addEventListener('mousemove', draw);\n            drawingCanvas.addEventListener('mouseup', stopDrawing);\n            drawingCanvas.addEventListener('mouseleave', stopDrawing); // Stop if mouse leaves canvas\n\n             // Touch events\n            drawingCanvas.addEventListener('touchstart', startDrawing);\n            drawingCanvas.addEventListener('touchmove', draw);\n            drawingCanvas.addEventListener('touchend', stopDrawing);\n            drawingCanvas.addEventListener('touchcancel', stopDrawing);\n\n\n            // --- Initial Setup ---\n            resizeCanvas(); // Set initial size and draw guides if checked\n            thicknessValue.textContent = maxThickness; // Set initial thickness display\n            document.body.dataset.exercise = 'free'; // Initial mode\n            document.body.dataset.text = '';\n\n        });\n    </script>\n\n</body>\n</html>"}, {"title": "Making friends", "url": "https://www.youtube.com/watch?v=I9hJ_Rux9y0", "spec": "Build an interactive web app to help users explore the principles of making and maintaining friendships, based on the Kurzgesagt video about friendship. \n\nSPECIFICATIONS:\n1.  The app should present the user with a series of scenarios, each representing a different stage or situation in the process of making and maintaining friendships. Scenarios should be drawn from the video's examples (e.g., attending a class, joining a club, moving to a new city, having a breakup, scrolling through contacts on your phone).\n\n2.  For each scenario, the app should pose a question to the user, prompting them to choose from a set of actions or behaviors.  Actions should be simple, concrete, and reflect either positive or negative friendship-building behaviors discussed in the video.\n\n3.  Each action chosen by the user should result in a visual or numerical representation of the impact on their friendship \"score\" or \"network\". This could be a simple bar graph or a network diagram showing connections growing or shrinking.  Positive actions increase the score, negative actions decrease it.\n\n4.  The app should track the user's overall friendship score throughout the scenarios. The user should receive feedback at the end of each scenario, and at the end of the game, based on their final score. This feedback can provide encouragement and tips for improving their friendship skills.\n\n5.  The app should incorporate at least three scenarios, and ideally closer to 5-7. The scenarios should be varied, and should cover both initiating new friendships and maintaining existing ones.\n\n6.  Keep the interface simple and visually appealing. Use colors and imagery that evoke a sense of warmth, connection, and fun, but avoid being overly sentimental or saccharine.\n\n7.  The actions presented should be easily understandable and should clearly relate back to the principles discussed in the video (e.g., prioritizing time, showing genuine interest, being open and receptive, inviting people to do fun things, sharing experiences).\n\n8. The app's logic and scoring mechanism should be transparent, so users understand how their actions impact their friendship network. Avoid arbitrary scoring, and instead focus on rewarding behaviors that are known to build strong relationships.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Friendship Explorer</title>\n    <style>\n        @import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap');\n\n        :root {\n            --primary-bg: #FFF8E1; /* Light Cream */\n            --secondary-bg: #FFECB3; /* Lighter Amber */\n            --accent-color: #FF8F00; /* Amber */\n            --text-color: #4E342E; /* Brown */\n            --positive-color: #4CAF50; /* Green */\n            --negative-color: #EF5350; /* Red */\n            --neutral-color: #78909C; /* Blue Grey */\n            --border-radius: 8px;\n            --box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n        }\n\n        body {\n            font-family: 'Lato', sans-serif;\n            background-color: var(--primary-bg);\n            color: var(--text-color);\n            margin: 0;\n            padding: 20px;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            min-height: 100vh;\n            line-height: 1.6;\n        }\n\n        #app-container {\n            background-color: #FFFFFF;\n            padding: 30px;\n            border-radius: var(--border-radius);\n            box-shadow: var(--box-shadow);\n            max-width: 600px;\n            width: 90%;\n            text-align: center;\n        }\n\n        h1 {\n            color: var(--accent-color);\n            margin-bottom: 10px;\n        }\n        h2 {\n            color: var(--text-color);\n            margin-bottom: 15px;\n            border-bottom: 2px solid var(--secondary-bg);\n            padding-bottom: 5px;\n        }\n        h3 {\n             color: var(--accent-color);\n             margin-top: 25px;\n             margin-bottom: 10px;\n        }\n\n        #scenario-container {\n            margin-bottom: 25px;\n            padding: 20px;\n            background-color: var(--secondary-bg);\n            border-radius: var(--border-radius);\n        }\n\n        #scenario-title {\n            font-size: 1.4em;\n            font-weight: bold;\n            color: var(--accent-color);\n            margin-bottom: 10px;\n        }\n\n        #scenario-text, #scenario-question {\n            margin-bottom: 15px;\n            font-size: 1.1em;\n        }\n\n        #actions-container button {\n            display: block;\n            width: 100%;\n            padding: 12px 15px;\n            margin: 10px 0;\n            border: none;\n            border-radius: var(--border-radius);\n            background-color: var(--accent-color);\n            color: white;\n            font-size: 1em;\n            font-weight: bold;\n            cursor: pointer;\n            transition: background-color 0.3s ease, transform 0.1s ease;\n            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n        }\n\n        #actions-container button:hover {\n            background-color: #FFA000; /* Darker Amber */\n            transform: translateY(-2px);\n        }\n        #actions-container button:active {\n            transform: translateY(0px);\n        }\n\n        #action-feedback {\n            margin-top: 15px;\n            font-weight: bold;\n            min-height: 20px; /* Reserve space */\n            transition: opacity 0.5s ease-in-out;\n        }\n\n        .feedback-positive { color: var(--positive-color); }\n        .feedback-negative { color: var(--negative-color); }\n        .feedback-neutral { color: var(--neutral-color); }\n\n\n        #score-container {\n            margin-top: 20px;\n        }\n\n        #score-bar-container {\n            width: 100%;\n            height: 30px;\n            background-color: var(--secondary-bg);\n            border-radius: var(--border-radius);\n            overflow: hidden;\n            margin-bottom: 10px;\n            border: 1px solid var(--accent-color);\n        }\n\n        #score-bar {\n            height: 100%;\n            width: 50%; /* Start at 50% */\n            background: linear-gradient(90deg, #FFCA28, var(--accent-color)); /* Yellow to Amber gradient */\n            border-radius: var(--border-radius) 0 0 var(--border-radius);\n            transition: width 0.5s ease-in-out;\n            text-align: right;\n            line-height: 30px;\n            color: white;\n            font-weight: bold;\n            padding-right: 5px;\n            box-sizing: border-box; /* Include padding in width */\n        }\n\n        #score-value {\n            font-weight: bold;\n            color: var(--text-color);\n        }\n\n        #end-game-container {\n            padding: 20px;\n            background-color: var(--secondary-bg);\n            border-radius: var(--border-radius);\n            margin-top: 20px;\n        }\n\n        #end-game-container h2 {\n            color: var(--accent-color);\n        }\n\n        #restart-button {\n             padding: 12px 25px;\n             margin-top: 20px;\n             border: none;\n             border-radius: var(--border-radius);\n             background-color: var(--positive-color);\n             color: white;\n             font-size: 1.1em;\n             font-weight: bold;\n             cursor: pointer;\n             transition: background-color 0.3s ease;\n        }\n         #restart-button:hover {\n            background-color: #388E3C; /* Darker Green */\n         }\n    </style>\n</head>\n<body>\n\n    <div id=\"app-container\">\n        <h1>Making friends</h1>\n        <p>Navigate scenarios based on Kurzgesagt's friendship principles!</p>\n\n        <div id=\"scenario-container\">\n            <h2 id=\"scenario-title\">Scenario Title</h2>\n            <p id=\"scenario-text\">Scenario description goes here.</p>\n            <p id=\"scenario-question\">Question goes here?</p>\n            <div id=\"actions-container\">\n                <!-- Action buttons will be generated here -->\n            </div>\n            <p id=\"action-feedback\">&nbsp;</p> <!-- Placeholder for feedback -->\n        </div>\n\n        <div id=\"score-container\">\n            <h3>Your Friendship Potential</h3>\n            <div id=\"score-bar-container\">\n                <div id=\"score-bar\"></div>\n            </div>\n            <span id=\"score-value\">Score: 50</span>\n        </div>\n\n        <div id=\"end-game-container\" style=\"display: none;\">\n            <h2>Journey Complete!</h2>\n            <p id=\"final-score\">Your final score is: X</p>\n            <p id=\"final-feedback\">Final feedback message goes here.</p>\n            <button id=\"restart-button\">Explore Again?</button>\n        </div>\n    </div>\n\n    <script>\n        const scenarios = [\n            {\n                title: \"New Beginnings\",\n                text: \"You've just moved to a new city for work. You don't know anyone yet. It's Saturday afternoon.\",\n                question: \"What do you do?\",\n                actions: [\n                    { text: \"Stay home, unpack, and watch a movie.\", scoreChange: -5, feedback: \"Comfortable, but limits opportunities. Proximity matters!\", feedbackClass: \"feedback-neutral\" },\n                    { text: \"Find a local hobby club (e.g., board games, hiking) online and sign up for their next meeting.\", scoreChange: 15, feedback: \"Great initiative! Putting yourself in new social contexts is key.\", feedbackClass: \"feedback-positive\" },\n                    { text: \"Go to a coffee shop and hope someone talks to you.\", scoreChange: 0, feedback: \"Better than staying home, but still passive. Active effort yields more results.\", feedbackClass: \"feedback-neutral\" }\n                ]\n            },\n            {\n                title: \"Making Contact\",\n                text: \"You're at that first meeting of the hobby club you joined. You see someone who seems interesting and shares a similar enthusiasm for the hobby.\",\n                question: \"How do you approach this?\",\n                actions: [\n                    { text: \"Wait for them to talk to you first, maybe make eye contact.\", scoreChange: -5, feedback: \"A missed opportunity. Don't wait for others to make the first move.\", feedbackClass: \"feedback-negative\" },\n                    { text: \"Smile and introduce yourself, asking about their interest in the hobby.\", scoreChange: 15, feedback: \"Excellent! Showing genuine interest and openness breaks the ice.\", feedbackClass: \"feedback-positive\" },\n                    { text: \"Just focus intensely on the hobby activity next to them.\", scoreChange: -2, feedback: \"Being present is good, but interaction requires reaching out.\", feedbackClass: \"feedback-neutral\" }\n                ]\n            },\n            {\n                title: \"Following Up\",\n                text: \"You had a nice chat with that person from the club. You exchanged contact info.\",\n                question: \"What's your next step?\",\n                actions: [\n                    { text: \"Wait a week or two to see if they contact you.\", scoreChange: -10, feedback: \"Momentum is lost. Friendships need nurturing.\", feedbackClass: \"feedback-negative\" },\n                    { text: \"Send a message the next day mentioning something from your chat and suggest meeting up for coffee or another club event.\", scoreChange: 15, feedback: \"Perfect! Taking initiative to suggest a shared activity strengthens the potential bond.\", feedbackClass: \"feedback-positive\" },\n                    { text: \"Add them on social media and just 'like' their posts occasionally.\", scoreChange: 0, feedback: \"Low effort, low reward. Doesn't build a real connection.\", feedbackClass: \"feedback-neutral\" }\n                ]\n            },\n            {\n                title: \"Busy Lives\",\n                text: \"You realize you haven't spoken to a good, established friend in over a month. Life has been hectic for both of you.\",\n                question: \"What do you do?\",\n                actions: [\n                    { text: \"Assume they're too busy and wait for them to reach out.\", scoreChange: -10, feedback: \"Friendships fade without effort. Don't let assumptions create distance.\", feedbackClass: \"feedback-negative\" },\n                    { text: \"Send a quick text: 'Hey, thinking of you! Been ages. How are things?'\", scoreChange: 10, feedback: \"Good! A small gesture shows you care and keeps the connection warm.\", feedbackClass: \"feedback-positive\" },\n                    { text: \"Send a message suggesting a specific time to catch up soon (call or meetup).\", scoreChange: 15, feedback: \"Even better! Proactively scheduling quality time is crucial for maintenance.\", feedbackClass: \"feedback-positive\" }\n                ]\n            },\n            {\n                title: \"Showing Support\",\n                text: \"A friend messages you, clearly upset about a recent breakup.\",\n                question: \"How do you respond?\",\n                actions: [\n                    { text: \"Send a generic 'Sorry to hear that' message.\", scoreChange: 0, feedback: \"It's something, but lacks depth. Vulnerability needs more support.\", feedbackClass: \"feedback-neutral\" },\n                    { text: \"Reply asking if they want to talk or hang out, offering to listen without judgment.\", scoreChange: 15, feedback: \"Supportive and present. Being there during tough times deepens trust.\", feedbackClass: \"feedback-positive\" },\n                    { text: \"Give them advice on how to get over it quickly.\", scoreChange: -5, feedback: \"Unsolicited advice can feel dismissive. Listening is often more valuable.\", feedbackClass: \"feedback-negative\" }\n                ]\n            },\n             {\n                title: \"Low-Effort Connection\",\n                text: \"You're scrolling through your phone contacts or social media feed and see the name of someone you like but haven't connected with deeply yet.\",\n                question: \"What's your move?\",\n                actions: [\n                    { text: \"Keep scrolling. You'll talk to them eventually.\", scoreChange: -5, feedback: \"'Eventually' often means 'never'. Small opportunities matter.\", feedbackClass: \"feedback-negative\" },\n                    { text: \"Send a quick, low-pressure message, maybe referencing a shared interest or memory.\", scoreChange: 10, feedback: \"Great! Small, consistent efforts maintain and build connections over time.\", feedbackClass: \"feedback-positive\" },\n                    { text: \"Make a mental note to talk to them later.\", scoreChange: -2, feedback: \"Mental notes get lost. Action, even small, is better.\", feedbackClass: \"feedback-neutral\" }\n                ]\n            }\n        ];\n\n        let currentScenarioIndex = 0;\n        let score = 50; // Start score\n        const maxScore = 100; // Maximum possible score\n        const minScore = 0; // Minimum possible score\n\n        const scenarioTitleEl = document.getElementById('scenario-title');\n        const scenarioTextEl = document.getElementById('scenario-text');\n        const scenarioQuestionEl = document.getElementById('scenario-question');\n        const actionsContainerEl = document.getElementById('actions-container');\n        const actionFeedbackEl = document.getElementById('action-feedback');\n\n        const scoreBarEl = document.getElementById('score-bar');\n        const scoreValueEl = document.getElementById('score-value');\n\n        const scenarioContainerEl = document.getElementById('scenario-container');\n        const scoreContainerEl = document.getElementById('score-container');\n        const endGameContainerEl = document.getElementById('end-game-container');\n        const finalScoreEl = document.getElementById('final-score');\n        const finalFeedbackEl = document.getElementById('final-feedback');\n        const restartButtonEl = document.getElementById('restart-button');\n\n        function updateScoreBar() {\n            // Clamp score between minScore and maxScore\n            score = Math.max(minScore, Math.min(maxScore, score));\n            const scorePercentage = (score / maxScore) * 100;\n            scoreBarEl.style.width = `${scorePercentage}%`;\n            // scoreBarEl.textContent = `${score}`; // Optional: Show score number inside bar\n            scoreValueEl.textContent = `Score: ${score}`;\n        }\n\n        function displayActionFeedback(feedback, feedbackClass) {\n            actionFeedbackEl.textContent = feedback;\n            actionFeedbackEl.className = feedbackClass; // Apply class for color\n            actionFeedbackEl.style.opacity = 1;\n        }\n\n        function loadScenario(index) {\n            // Clear previous feedback immediately\n            actionFeedbackEl.style.opacity = 0;\n            actionFeedbackEl.textContent = ' '; // Non-breaking space to maintain height\n\n            if (index >= scenarios.length) {\n                endGame();\n                return;\n            }\n\n            const scenario = scenarios[index];\n            scenarioTitleEl.textContent = scenario.title;\n            scenarioTextEl.textContent = scenario.text;\n            scenarioQuestionEl.textContent = scenario.question;\n\n            actionsContainerEl.innerHTML = ''; // Clear old buttons\n\n            scenario.actions.forEach(action => {\n                const button = document.createElement('button');\n                button.textContent = action.text;\n                button.onclick = () => handleAction(action.scoreChange, action.feedback, action.feedbackClass);\n                actionsContainerEl.appendChild(button);\n            });\n        }\n\n        function handleAction(scoreChange, feedback, feedbackClass) {\n            score += scoreChange;\n            updateScoreBar();\n            displayActionFeedback(feedback, feedbackClass);\n\n            // Disable buttons temporarily after click\n            actionsContainerEl.querySelectorAll('button').forEach(btn => btn.disabled = true);\n\n            // Delay before loading next scenario to allow user to read feedback\n            setTimeout(() => {\n                 // Re-enable buttons (although they will be replaced)\n                actionsContainerEl.querySelectorAll('button').forEach(btn => btn.disabled = false);\n                currentScenarioIndex++;\n                loadScenario(currentScenarioIndex);\n            }, 2000); // 2-second delay\n        }\n\n        function getFinalFeedback(finalScore) {\n            if (finalScore >= 85) {\n                return \"Fantastic! You're a natural connector. You understand the importance of initiative, consistency, and support in building strong friendships. Keep nurturing those bonds!\";\n            } else if (finalScore >= 65) {\n                return \"Great job! You have solid friendship-building instincts. Focus on consistently applying those principles, especially taking initiative and prioritizing time, to strengthen your network even further.\";\n            } else if (finalScore >= 40) {\n                return \"You're on the right track! Sometimes you take great steps, other times you hesitate. Remember that small, consistent efforts and reaching out proactively make a big difference. Don't be afraid to make the first move!\";\n            } else {\n                return \"Building friendships takes practice. It seems you might be hesitant or unsure how to initiate and maintain connections. Focus on the basics: show up, be open, express genuine interest, and take small steps to invite others into your life. Every interaction is a chance to learn!\";\n            }\n        }\n\n        function endGame() {\n            scenarioContainerEl.style.display = 'none';\n            scoreContainerEl.style.display = 'none'; // Hide score bar too\n            endGameContainerEl.style.display = 'block';\n\n            finalScoreEl.textContent = `Your final Friendship Potential Score is: ${score}`;\n            finalFeedbackEl.textContent = getFinalFeedback(score);\n        }\n\n        function restartGame() {\n            currentScenarioIndex = 0;\n            score = 50; // Reset score\n            updateScoreBar();\n\n            endGameContainerEl.style.display = 'none';\n            scenarioContainerEl.style.display = 'block';\n            scoreContainerEl.style.display = 'block'; // Show score bar again\n\n            loadScenario(currentScenarioIndex);\n        }\n\n        // Initial load\n        restartButtonEl.addEventListener('click', restartGame);\n        loadScenario(currentScenarioIndex);\n        updateScoreBar(); // Initialize score bar display\n\n    </script>\n\n</body>\n</html>"}, {"title": "Hit more home runs", "url": "https://www.youtube.com/watch?v=zg_tqDklGcs", "spec": "Build me an interactive web app to help a learner understand proper baseball hitting mechanics, specifically related to shoulder tilt and barrel tilt. \n\nSPECIFICATIONS:\n1.  The app must feature a simplified, animated baseball player (stick figure or similarly simple design is sufficient).\n2.  The player will be in a batting stance, facing the user. The app should allow the user to adjust two parameters: shoulder tilt and barrel tilt. For each, provide a slider or similar control that ranges from -20 degrees to +20 degrees, with 0 representing a \"flat\" (i.e., level) orientation.\n3.  As the user adjusts the \"shoulder tilt\" slider, the animated player's shoulders should rotate accordingly. The animation should clearly show the front shoulder moving up or down relative to the back shoulder. Provide a text label below the animation that reads \"Shoulder Tilt: [X] degrees\", where [X] is the current value of the slider.\n4.  Similarly, as the user adjusts the \"barrel tilt\" slider, the animated player's bat should rotate accordingly. The animation should clearly show the barrel of the bat tilting in towards the plate or away from the plate. Provide a text label below the animation that reads \"Barrel Tilt: [Y] degrees\", where [Y] is the current value of the slider.\n5.  The app should include helpful text explaining the proper tilt of each control:\n    *   For Shoulder Tilt: \"Ideal shoulder tilt at launch position is slightly downward (approximately 9 degrees).\"\n    *   For Barrel Tilt: \"Ideal barrel tilt at launch position is slightly inward (towards the plate).\"\n6.  The app should include a button labeled \"Show Ideal Position\". When clicked, the app should smoothly animate the player to the ideal settings (shoulder tilt: -9 degrees, barrel tilt: a small positive value) and display a message stating: \"This is an example of the proper shoulder and barrel tilt at the launch position.\"\n7.  The app should include a button labeled \"Reset Position\". When clicked, both sliders should return to 0 and the animation of the player should return to a flat shoulder and barrel position.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Baseball Hitting Mechanics: Tilt Trainer</title>\n    <style>\n        body {\n            font-family: sans-serif;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            min-height: 100vh;\n            background-color: #f0f0f0;\n            color: #333;\n        }\n        #app-container {\n            background-color: #fff;\n            padding: 30px;\n            border-radius: 10px;\n            box-shadow: 0 4px 10px rgba(0,0,0,0.1);\n            text-align: center;\n            max-width: 600px;\n        }\n        canvas {\n            border: 1px solid #ccc;\n            background-color: #e8f4f8; /* Light blue sky-like background */\n            display: block;\n            margin: 20px auto;\n        }\n        .controls {\n            margin-top: 20px;\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 20px;\n            align-items: start;\n        }\n        .control-group {\n            padding: 15px;\n            border: 1px solid #eee;\n            border-radius: 5px;\n        }\n        label {\n            display: block;\n            margin-bottom: 5px;\n            font-weight: bold;\n        }\n        input[type=\"range\"] {\n            width: 100%;\n            cursor: pointer;\n        }\n        .value-display {\n            display: block;\n            margin-top: 5px;\n            font-size: 0.9em;\n            color: #555;\n            min-height: 1.2em; /* Prevent layout shift */\n        }\n        .explanation {\n            font-size: 0.9em;\n            color: #666;\n            margin-top: 10px;\n            text-align: left;\n            background-color: #f9f9f9;\n            padding: 8px;\n            border-radius: 4px;\n            border-left: 3px solid #007bff;\n        }\n        .buttons {\n            margin-top: 25px;\n            display: flex;\n            justify-content: center;\n            gap: 15px;\n        }\n        button {\n            padding: 10px 20px;\n            font-size: 1em;\n            cursor: pointer;\n            border: none;\n            border-radius: 5px;\n            transition: background-color 0.3s ease;\n        }\n        #ideal-btn {\n            background-color: #28a745;\n            color: white;\n        }\n        #ideal-btn:hover {\n            background-color: #218838;\n        }\n        #reset-btn {\n            background-color: #dc3545;\n            color: white;\n        }\n        #reset-btn:hover {\n            background-color: #c82333;\n        }\n        #message-area {\n            margin-top: 15px;\n            font-weight: bold;\n            color: #007bff;\n            min-height: 1.5em; /* Prevent layout shift */\n        }\n    </style>\n</head>\n<body>\n    <div id=\"app-container\">\n        <h1>Baseball Hitting Tilt Trainer</h1>\n        <canvas id=\"hitterCanvas\" width=\"300\" height=\"350\"></canvas>\n        <div id=\"message-area\"></div>\n        <div class=\"controls\">\n            <div class=\"control-group\">\n                <label for=\"shoulder-tilt\">Shoulder Tilt:</label>\n                <input type=\"range\" id=\"shoulder-tilt\" name=\"shoulder-tilt\" min=\"-20\" max=\"20\" value=\"0\">\n                <span class=\"value-display\" id=\"shoulder-tilt-value\">Shoulder Tilt: 0 degrees</span>\n                <div class=\"explanation\">\n                    Ideal shoulder tilt at launch position is slightly downward (approximately -9 degrees).\n                </div>\n            </div>\n            <div class=\"control-group\">\n                <label for=\"barrel-tilt\">Barrel Tilt:</label>\n                <input type=\"range\" id=\"barrel-tilt\" name=\"barrel-tilt\" min=\"-20\" max=\"20\" value=\"0\">\n                <span class=\"value-display\" id=\"barrel-tilt-value\">Barrel Tilt: 0 degrees</span>\n                <div class=\"explanation\">\n                    Ideal barrel tilt at launch position is slightly inward towards the plate (a small positive value, e.g., 5-15 degrees).\n                </div>\n            </div>\n        </div>\n        <div class=\"buttons\">\n            <button id=\"ideal-btn\">Show Ideal Position</button>\n            <button id=\"reset-btn\">Reset Position</button>\n        </div>\n    </div>\n\n    <script>\n        // --- DOM Elements ---\n        const canvas = document.getElementById('hitterCanvas');\n        const ctx = canvas.getContext('2d');\n        const shoulderTiltSlider = document.getElementById('shoulder-tilt');\n        const barrelTiltSlider = document.getElementById('barrel-tilt');\n        const shoulderTiltValueDisplay = document.getElementById('shoulder-tilt-value');\n        const barrelTiltValueDisplay = document.getElementById('barrel-tilt-value');\n        const idealBtn = document.getElementById('ideal-btn');\n        const resetBtn = document.getElementById('reset-btn');\n        const messageArea = document.getElementById('message-area');\n\n        // --- Constants ---\n        const CANVAS_WIDTH = canvas.width;\n        const CANVAS_HEIGHT = canvas.height;\n        const CENTER_X = CANVAS_WIDTH / 2;\n        const CENTER_Y = CANVAS_HEIGHT / 2 + 30; // Adjust vertical position\n        const HEAD_RADIUS = 15;\n        const TORSO_HEIGHT = 70;\n        const SHOULDER_WIDTH = 50;\n        const ARM_LENGTH = 60;\n        const LEG_LENGTH = 80;\n        const STANCE_WIDTH = 40;\n        const BAT_LENGTH = 100;\n        const BAT_THICKNESS = 6;\n        const LINE_THICKNESS = 3;\n\n        const IDEAL_SHOULDER_TILT = -9;\n        const IDEAL_BARREL_TILT = 10; // Example small positive value\n        const ANIMATION_DURATION = 500; // ms\n\n        // --- State ---\n        let shoulderTilt = 0;\n        let barrelTilt = 0;\n        let animationFrameId = null;\n\n        // --- Drawing Functions ---\n        function clearCanvas() {\n            ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);\n        }\n\n        function drawPlayer(sTilt, bTilt) {\n            clearCanvas();\n            ctx.lineWidth = LINE_THICKNESS;\n            ctx.strokeStyle = '#333';\n            ctx.fillStyle = '#333';\n\n            // Convert degrees to radians\n            const shoulderAngleRad = sTilt * Math.PI / 180;\n            // Base bat angle (slightly up and back) + barrel tilt\n            const baseBatAngle = -60; // Degrees relative to horizontal\n            const totalBatAngleRad = (baseBatAngle + bTilt) * Math.PI / 180;\n\n            // --- Body Parts Calculation ---\n            const headCenter = { x: CENTER_X, y: CENTER_Y - TORSO_HEIGHT / 2 - HEAD_RADIUS };\n            const neckPos = { x: CENTER_X, y: headCenter.y + HEAD_RADIUS };\n            const hipPos = { x: CENTER_X, y: neckPos.y + TORSO_HEIGHT };\n\n            // Shoulders (rotate around neckPos)\n            const shoulderOffsetY = (SHOULDER_WIDTH / 2) * Math.sin(shoulderAngleRad);\n            const shoulderOffsetX = (SHOULDER_WIDTH / 2) * Math.cos(shoulderAngleRad);\n\n            const frontShoulder = { x: neckPos.x - shoulderOffsetX, y: neckPos.y - shoulderOffsetY }; // User's left, player's right\n            const backShoulder = { x: neckPos.x + shoulderOffsetX, y: neckPos.y + shoulderOffsetY };   // User's right, player's left\n\n            // Arms (simple lines for now)\n            // Place hands near back shoulder for grip point approximation\n            const handPos = {\n                x: backShoulder.x + 10 * Math.cos(shoulderAngleRad) - 5, // Slightly offset from back shoulder\n                y: backShoulder.y + 10 * Math.sin(shoulderAngleRad) + 10\n            };\n\n            const frontElbow = { x: frontShoulder.x - 10, y: frontShoulder.y + ARM_LENGTH * 0.5 };\n            const backElbow = { x: backShoulder.x + 10, y: backShoulder.y + ARM_LENGTH * 0.5 };\n\n            // Legs\n            const frontFoot = { x: CENTER_X - STANCE_WIDTH / 2, y: hipPos.y + LEG_LENGTH };\n            const backFoot = { x: CENTER_X + STANCE_WIDTH / 2, y: hipPos.y + LEG_LENGTH };\n\n            // Bat\n            const batEndX = handPos.x + BAT_LENGTH * Math.cos(totalBatAngleRad);\n            const batEndY = handPos.y + BAT_LENGTH * Math.sin(totalBatAngleRad); // +sin moves down\n\n            // --- Drawing ---\n            ctx.lineCap = 'round';\n            ctx.lineJoin = 'round';\n\n            // Legs\n            ctx.beginPath();\n            ctx.moveTo(hipPos.x, hipPos.y);\n            ctx.lineTo(frontFoot.x, frontFoot.y);\n            ctx.moveTo(hipPos.x, hipPos.y);\n            ctx.lineTo(backFoot.x, backFoot.y);\n            ctx.stroke();\n\n            // Torso\n            ctx.beginPath();\n            ctx.moveTo(neckPos.x, neckPos.y);\n            ctx.lineTo(hipPos.x, hipPos.y);\n            ctx.stroke();\n\n            // Shoulders\n            ctx.beginPath();\n            ctx.moveTo(frontShoulder.x, frontShoulder.y);\n            ctx.lineTo(backShoulder.x, backShoulder.y);\n            ctx.stroke();\n\n            // Arms\n            ctx.beginPath();\n            ctx.moveTo(frontShoulder.x, frontShoulder.y);\n            ctx.lineTo(frontElbow.x, frontElbow.y);\n            ctx.lineTo(handPos.x - 5, handPos.y - 5); // Approx front hand near grip\n            ctx.moveTo(backShoulder.x, backShoulder.y);\n            ctx.lineTo(backElbow.x, backElbow.y);\n            ctx.lineTo(handPos.x, handPos.y); // Back hand at grip\n            ctx.stroke();\n\n             // Head\n             ctx.beginPath();\n             ctx.arc(headCenter.x, headCenter.y, HEAD_RADIUS, 0, Math.PI * 2);\n             ctx.fillStyle = '#f0f0f0'; // Match background slightly\n             ctx.fill();\n             ctx.stroke();\n             // Simple eye line for facing direction\n             ctx.beginPath();\n             ctx.moveTo(headCenter.x - HEAD_RADIUS * 0.4, headCenter.y);\n             ctx.lineTo(headCenter.x + HEAD_RADIUS * 0.4, headCenter.y);\n             ctx.lineWidth = 1.5;\n             ctx.stroke();\n\n\n            // Bat\n            ctx.beginPath();\n            ctx.moveTo(handPos.x, handPos.y);\n            ctx.lineTo(batEndX, batEndY);\n            ctx.lineWidth = BAT_THICKNESS; // Thicker bat\n            ctx.strokeStyle = '#8B4513'; // Brown color for bat\n            ctx.stroke();\n            ctx.lineWidth = LINE_THICKNESS; // Reset line width\n            ctx.strokeStyle = '#333'; // Reset color\n\n        }\n\n        // --- Update Functions ---\n        function updateDisplays() {\n            shoulderTiltValueDisplay.textContent = `Shoulder Tilt: ${shoulderTilt.toFixed(1)} degrees`;\n            barrelTiltValueDisplay.textContent = `Barrel Tilt: ${barrelTilt.toFixed(1)} degrees`;\n        }\n\n        function updateState(newShoulderTilt, newBarrelTilt) {\n            shoulderTilt = newShoulderTilt;\n            barrelTilt = newBarrelTilt;\n            shoulderTiltSlider.value = shoulderTilt;\n            barrelTiltSlider.value = barrelTilt;\n            updateDisplays();\n            drawPlayer(shoulderTilt, barrelTilt);\n        }\n\n        // --- Animation Function ---\n        function animateTo(targetShoulder, targetBarrel, duration) {\n            if (animationFrameId) {\n                cancelAnimationFrame(animationFrameId); // Cancel previous animation\n            }\n            messageArea.textContent = ''; // Clear message during animation\n\n            const startShoulder = shoulderTilt;\n            const startBarrel = barrelTilt;\n            const shoulderChange = targetShoulder - startShoulder;\n            const barrelChange = targetBarrel - startBarrel;\n            let startTime = null;\n\n            function step(timestamp) {\n                if (!startTime) startTime = timestamp;\n                const elapsed = timestamp - startTime;\n                const progress = Math.min(elapsed / duration, 1); // Ensure progress doesn't exceed 1\n\n                 // Simple easing (ease-out cubic)\n                const easedProgress = 1 - Math.pow(1 - progress, 3);\n\n                const currentShoulder = startShoulder + shoulderChange * easedProgress;\n                const currentBarrel = startBarrel + barrelChange * easedProgress;\n\n                // Update state variables directly for drawing\n                shoulderTilt = currentShoulder;\n                barrelTilt = currentBarrel;\n\n                // Update sliders and displays to follow animation\n                shoulderTiltSlider.value = currentShoulder;\n                barrelTiltSlider.value = currentBarrel;\n                updateDisplays(); // Update text displays during animation\n\n                drawPlayer(currentShoulder, currentBarrel);\n\n                if (progress < 1) {\n                    animationFrameId = requestAnimationFrame(step);\n                } else {\n                    // Ensure final state is exact\n                    updateState(targetShoulder, targetBarrel);\n                    animationFrameId = null;\n                    if (targetShoulder === IDEAL_SHOULDER_TILT && targetBarrel === IDEAL_BARREL_TILT) {\n                         messageArea.textContent = \"This is an example of the proper shoulder and barrel tilt at the launch position.\";\n                    }\n                }\n            }\n\n            animationFrameId = requestAnimationFrame(step);\n        }\n\n\n        // --- Event Listeners ---\n        shoulderTiltSlider.addEventListener('input', (e) => {\n            if (animationFrameId) cancelAnimationFrame(animationFrameId); // Stop animation if user interacts\n            messageArea.textContent = '';\n            shoulderTilt = parseFloat(e.target.value);\n            updateDisplays();\n            drawPlayer(shoulderTilt, barrelTilt);\n        });\n\n        barrelTiltSlider.addEventListener('input', (e) => {\n            if (animationFrameId) cancelAnimationFrame(animationFrameId); // Stop animation if user interacts\n            messageArea.textContent = '';\n            barrelTilt = parseFloat(e.target.value);\n            updateDisplays();\n            drawPlayer(shoulderTilt, barrelTilt);\n        });\n\n        idealBtn.addEventListener('click', () => {\n            animateTo(IDEAL_SHOULDER_TILT, IDEAL_BARREL_TILT, ANIMATION_DURATION);\n        });\n\n        resetBtn.addEventListener('click', () => {\n            animateTo(0, 0, ANIMATION_DURATION / 2); // Faster reset animation\n            messageArea.textContent = '';\n        });\n\n        // --- Initial Draw ---\n        updateState(0, 0); // Initialize sliders, displays, and draw initial state\n\n    </script>\n</body>\n</html>"}, {"title": "Tie your shoes", "url": "https://www.youtube.com/watch?v=q44kByZmKDs", "spec": "The video demonstrates how to tie a shoelace using the bunny ears method. The key idea is to break down the process into manageable steps that can be practiced. \n\nBuild me an interactive web app that reinforces the steps involved in tying a shoelace.\n\nSPECIFICATIONS:\n1. The app should display a shoelace tying process as a series of interactive steps.\n2. The app should feature two shoelaces that the user can manipulate.\n3. The app should have a button for each step in the process, and the user must press the buttons in the correct sequence. When the user presses the correct button, the shoelaces will move to reflect the action. When the user presses the incorrect button, the app should play an error sound and display a message indicating the error.\n4. The app must guide the user through these steps:\n\t*   Grab one lace in each hand, about halfway up the lace, to form a V.\n\t*   Cross the laces to make an X. One lace will be in front, and the other lace is behind it.\n\t*   Take the lace that is behind and pull it over the front and put it through the hole.\n\t*   Take both ends and pull until the X becomes a V again.\n\t*   Spread the V out.\n\t*   Take one lace at the halfway point and let it hang on your finger.\n\t*   Pinch both sides of the lace so it makes a loop around your finger.\n\t*   Pull your finger out, and you've got an open loop. Hold on to it.\n\t*   Take the other lace and lay it over your thumb and finger so it's in front of the loop.\n\t*   Use your finger to send that lace back to the other side.\n\t*   Now the laces point opposite ways again, and you've made a little hole between your shoe and your thumb.\n\t*   Poke the lace through the little hole just a little bit, so it makes a loose loop.\n\t*   Grab both loops at the same time and pull until the knot is tight.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Interactive Shoelace Tying</title>\n    <style>\n        body {\n            font-family: sans-serif;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            padding: 20px;\n            background-color: #f0f0f0;\n        }\n\n        h1 {\n            color: #333;\n        }\n\n        #shoe-area {\n            width: 400px;\n            height: 350px;\n            border: 1px solid #ccc;\n            background-color: #fff;\n            margin-bottom: 20px;\n            position: relative; /* For potential absolute positioning inside if needed */\n            overflow: hidden; /* Hide parts of laces going outside */\n        }\n\n        #shoelace-svg {\n            width: 100%;\n            height: 100%;\n        }\n\n        /* Basic lace styling */\n        .lace {\n            fill: none;\n            stroke-width: 8;\n            stroke-linecap: round;\n            stroke-linejoin: round;\n            transition: d 0.5s ease-in-out; /* Animate path changes */\n        }\n\n        #lace-left {\n            stroke: #4a90e2; /* Blue */\n        }\n\n        #lace-right {\n            stroke: #f5a623; /* Orange */\n        }\n\n        /* Represent shoe eyelets */\n        .eyelet {\n            fill: #ddd;\n            stroke: #aaa;\n            stroke-width: 1;\n        }\n\n        #steps-container {\n            display: flex;\n            flex-wrap: wrap;\n            justify-content: center;\n            gap: 10px;\n            margin-bottom: 20px;\n            max-width: 600px;\n        }\n\n        .step-button {\n            padding: 10px 15px;\n            font-size: 14px;\n            cursor: pointer;\n            border: 1px solid #ccc;\n            background-color: #e9e9e9;\n            border-radius: 5px;\n            transition: background-color 0.2s, border-color 0.2s;\n        }\n\n        .step-button:hover:not(:disabled) {\n            background-color: #dcdcdc;\n        }\n\n        .step-button:disabled {\n            cursor: not-allowed;\n            opacity: 0.6;\n        }\n\n        .step-button.completed {\n            background-color: #c8e6c9; /* Light green */\n            border-color: #a5d6a7;\n        }\n\n        .step-button.next-step {\n            background-color: #fff9c4; /* Light yellow */\n            border-color: #fff176;\n            font-weight: bold;\n        }\n\n        #message-area {\n            min-height: 40px;\n            padding: 10px;\n            border: 1px solid transparent;\n            border-radius: 5px;\n            text-align: center;\n            width: 80%;\n            max-width: 580px;\n            background-color: #e3f2fd; /* Light blue info box */\n            color: #1e88e5;\n            font-weight: bold;\n        }\n\n        #message-area.error {\n            background-color: #ffebee; /* Light red */\n            color: #e53935;\n            border-color: #ef9a9a;\n        }\n\n        #reset-button {\n            margin-top: 15px;\n            padding: 8px 20px;\n            font-size: 16px;\n            cursor: pointer;\n            background-color: #80cbc4; /* Teal */\n            color: white;\n            border: none;\n            border-radius: 5px;\n        }\n         #reset-button:hover {\n             background-color: #4db6ac;\n         }\n\n    </style>\n</head>\n<body>\n\n    <h1>Learn to tie your shoelaces!</h1>\n    <p>(Bunny Ears Method)</p>\n\n    <div id=\"shoe-area\">\n        <svg id=\"shoelace-svg\" viewBox=\"0 0 400 350\">\n            <!-- Eyelets -->\n            <circle class=\"eyelet\" cx=\"150\" cy=\"300\" r=\"8\"/>\n            <circle class=\"eyelet\" cx=\"250\" cy=\"300\" r=\"8\"/>\n\n            <!-- Laces -->\n            <path id=\"lace-left\" class=\"lace\" d=\"M 150 300 L 100 150\" />\n            <path id=\"lace-right\" class=\"lace\" d=\"M 250 300 L 300 150\" />\n        </svg>\n    </div>\n\n    <div id=\"message-area\">Press the first button to start!</div>\n\n    <div id=\"steps-container\">\n        <!-- Buttons will be generated by JavaScript -->\n    </div>\n\n    <button id=\"reset-button\">Reset</button>\n\n    <!-- Error sound (Base64 encoded short beep/buzz) -->\n    <audio id=\"error-sound\" preload=\"auto\">\n        <source src=\"data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU...\" type=\"audio/wav\">\n        <!-- A minimal valid WAV header - provides a silent placeholder if the full data below fails -->\n         <!-- Full Beep Sound (Replace placeholder above if needed, example below is short) -->\n         <!-- Find a short 'error' wav/mp3 online and convert to Base64 using an online tool -->\n         <!-- Example (short ~0.1s beep): -->\n         <source src=\"data:audio/wav;base64,UklGRlIAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhQgAAAAI=\" type=\"audio/wav\">\n         Your browser does not support the audio element.\n    </audio>\n\n\n    <script>\n        const svg = document.getElementById('shoelace-svg');\n        const laceLeft = document.getElementById('lace-left');\n        const laceRight = document.getElementById('lace-right');\n        const stepsContainer = document.getElementById('steps-container');\n        const messageArea = document.getElementById('message-area');\n        const errorSound = document.getElementById('error-sound');\n        const resetButton = document.getElementById('reset-button');\n\n        let currentStepIndex = 0;\n        let buttons = [];\n\n        // --- Define the steps and corresponding SVG path data ---\n        // Note: 'd' attributes define the path shape. M=Move, L=Line, Q=Quadratic Bezier, C=Cubic Bezier\n        // These paths are simplified representations. Perfect realism is complex.\n        const steps = [\n            {\n                id: 'step0-initial', // Internal state, not a button\n                left_d: \"M 150 300 L 100 150\",\n                right_d: \"M 250 300 L 300 150\",\n                instruction: \"Press the first button to start!\" // Instruction for *before* step 1\n            },\n            {\n                id: 'step1-v',\n                buttonText: \"1. Form a V\",\n                left_d: \"M 150 300 Q 150 200 170 150 L 150 50\", // Pull up left\n                right_d: \"M 250 300 Q 250 200 230 150 L 250 50\", // Pull up right\n                instruction: \"Good! Now cross the laces to make an X.\"\n            },\n            {\n                id: 'step2-x',\n                buttonText: \"2. Make an X\",\n                // Left lace goes over right lace visually (drawn later = on top)\n                left_d: \"M 150 300 Q 180 200 250 150 L 280 50\", // Left crosses right\n                right_d: \"M 250 300 Q 220 200 150 150 L 120 50\", // Right crosses under left\n                instruction: \"Take the lace behind (orange) and tuck it through the hole.\"\n            },\n            {\n                id: 'step3-tuck',\n                buttonText: \"3. Tuck Under\",\n                 // Right (orange) lace tucks under and through\n                left_d: \"M 150 300 Q 180 200 250 150 L 280 50\", // Stays crossed over\n                right_d: \"M 250 300 Q 200 220 200 180 Q 200 220 150 150 L 120 50\", // Shows tucking motion path\n                instruction: \"Pull both ends tight to form a knot.\"\n            },\n            {\n                id: 'step4-tighten-knot',\n                buttonText: \"4. Pull Tight (Knot)\",\n                // Pull the 'X' down into a tight knot near eyelets\n                left_d: \"M 150 300 L 200 280 L 100 200\", // Pulled tight, angled out\n                right_d: \"M 250 300 L 200 280 L 300 200\", // Pulled tight, angled out\n                instruction: \"Spread the laces out again.\"\n            },\n             {\n                id: 'step5-spread',\n                buttonText: \"5. Spread V\",\n                 // Similar to step 1 but lower down, starting from knot\n                left_d: \"M 150 300 L 200 280 L 100 150\", // Spread out left\n                right_d: \"M 250 300 L 200 280 L 300 150\", // Spread out right\n                instruction: \"Make a loop ('bunny ear') with one lace (blue).\"\n            },\n            {\n                id: 'step6-loop1-prep', // Steps 6, 7, 8 combined visually\n                buttonText: \"6. Make First Loop\",\n                // Form a loop with the left (blue) lace\n                left_d: \"M 150 300 L 200 280 C 150 250 100 150 150 100 C 200 150 170 250 200 280\", // Blue loop\n                right_d: \"M 250 300 L 200 280 L 300 150\", // Right lace still straight\n                instruction: \"Hold the loop. Now take the other lace (orange).\"\n            },\n             { // Steps 7 & 8 are implicit in holding the loop made in step 6 visual\n                id: 'step9-lace-over',\n                buttonText: \"7. Other Lace Over\",\n                // Right (orange) lace goes over the base of the blue loop\n                left_d: \"M 150 300 L 200 280 C 150 250 100 150 150 100 C 200 150 170 250 200 280\", // Blue loop stays\n                right_d: \"M 250 300 L 200 280 Q 250 250 230 200 L 170 200\", // Orange goes across\n                instruction: \"Send the orange lace back around behind the blue loop.\"\n            },\n             { // Steps 10 & 11 combined visually\n                id: 'step10-send-back',\n                buttonText: \"8. Send Lace Back\",\n                 // Right (orange) lace goes behind, creating the 'hole'\n                left_d: \"M 150 300 L 200 280 C 150 250 100 150 150 100 C 200 150 170 250 200 280\", // Blue loop\n                right_d: \"M 250 300 L 200 280 Q 250 250 280 200 L 280 150\", // Orange pulls back slightly (implying going behind)\n                instruction: \"Poke the orange lace through the hole to make a second loop.\"\n            },\n            { // Step 12\n                id: 'step12-poke-loop2',\n                buttonText: \"9. Make Second Loop\",\n                // Orange lace forms the second loop by poking through\n                left_d: \"M 150 300 L 200 280 C 150 250 100 150 150 100 C 200 150 170 250 200 280\", // Blue loop\n                 // Orange loop forms, goes through hole near center\n                right_d: \"M 250 300 L 200 280 C 250 250 300 150 250 100 C 200 150 230 250 200 280\", // Orange loop\n                instruction: \"Almost there! Grab both loops and pull tight.\"\n            },\n            { // Step 13\n                id: 'step13-pull-tight',\n                buttonText: \"10. Pull Loops Tight\",\n                // Final bow knot, loops pulled tight\n                left_d: \"M 150 300 L 200 280 C 170 260 140 200 170 180 C 200 200 190 260 200 280\", // Tight blue loop\n                right_d: \"M 250 300 L 200 280 C 230 260 260 200 230 180 C 200 200 210 260 200 280\", // Tight orange loop\n                instruction: \"Congratulations! You tied the shoelace!\"\n            }\n        ];\n\n        function initializeApp() {\n            currentStepIndex = 0;\n            stepsContainer.innerHTML = ''; // Clear existing buttons\n            buttons = []; // Clear button array\n\n            // Set initial lace position (state 0)\n            updateLaces(steps[0].left_d, steps[0].right_d);\n            setInstruction(steps[0].instruction);\n            messageArea.classList.remove('error');\n\n\n            // Create buttons for steps 1 onwards\n            steps.slice(1).forEach((step, index) => {\n                const button = document.createElement('button');\n                button.textContent = step.buttonText;\n                button.classList.add('step-button');\n                button.dataset.stepIndex = index + 1; // Store the step index (starting from 1)\n                button.disabled = true; // Disable all buttons initially\n                button.addEventListener('click', handleStepClick);\n                stepsContainer.appendChild(button);\n                buttons.push(button);\n            });\n\n            // Enable the first button\n            if (buttons.length > 0) {\n                buttons[0].disabled = false;\n                buttons[0].classList.add('next-step');\n                 setInstruction(\"Press the first button: \" + buttons[0].textContent); // Update instruction\n            }\n             resetButton.disabled = false;\n        }\n\n        function handleStepClick(event) {\n            const clickedButton = event.target;\n            const clickedStepIndex = parseInt(clickedButton.dataset.stepIndex, 10);\n\n            // Check if the clicked button corresponds to the current expected step\n            if (clickedStepIndex === currentStepIndex + 1) {\n                // Correct step\n                const stepData = steps[clickedStepIndex];\n\n                // Update SVG\n                updateLaces(stepData.left_d, stepData.right_d);\n\n                // Update button states\n                clickedButton.disabled = true;\n                clickedButton.classList.remove('next-step');\n                clickedButton.classList.add('completed');\n\n                // Update message area\n                setInstruction(stepData.instruction);\n                messageArea.classList.remove('error');\n\n                // Move to the next step\n                currentStepIndex++;\n\n                // Enable the next button if it exists\n                if (currentStepIndex < steps.length -1) {\n                    buttons[currentStepIndex].disabled = false;\n                    buttons[currentStepIndex].classList.add('next-step');\n                    // Prepend instruction with next button text\n                    setInstruction(\"Next: \" + buttons[currentStepIndex].textContent + \". \" + steps[currentStepIndex + 1].instruction);\n                } else {\n                    // Last step completed\n                    setInstruction(stepData.instruction); // Show final congratulations\n                    resetButton.focus(); // Maybe focus reset button\n                }\n\n            } else {\n                // Incorrect step\n                playErrorSound();\n                messageArea.textContent = `Oops! That's not the right step. Try pressing the highlighted button.`;\n                messageArea.classList.add('error');\n                // Maybe add a visual cue to the button later (e.g., shake)\n            }\n        }\n\n        function updateLaces(leftD, rightD) {\n            // Ensure laces are drawn in the correct order for overlap (right over left initially, then left over right in X)\n            if (currentStepIndex + 1 >= 2 && currentStepIndex + 1 < 4) { // Steps 2 (X) and 3 (Tuck)\n                 svg.appendChild(laceRight); // Draw right first (under)\n                 svg.appendChild(laceLeft); // Draw left second (over)\n            } else if (currentStepIndex + 1 >= 7 && currentStepIndex + 1 < 9) { // Steps 7 (Orange Over) and 8 (Send Back)\n                 svg.appendChild(laceLeft); // Draw blue loop first (under)\n                 svg.appendChild(laceRight); // Draw orange crossing second (over)\n            }\n             else {\n                 svg.appendChild(laceLeft); // Default: draw left first\n                 svg.appendChild(laceRight); // Draw right second\n            }\n            laceLeft.setAttribute('d', leftD);\n            laceRight.setAttribute('d', rightD);\n        }\n\n\n        function setInstruction(text) {\n            messageArea.textContent = text;\n        }\n\n        function playErrorSound() {\n            try {\n                // Reset playback position and play\n                errorSound.currentTime = 0;\n                errorSound.play();\n            } catch (e) {\n                console.error(\"Error playing sound:\", e);\n                // Fallback or ignore if audio fails\n            }\n        }\n\n        // Reset button functionality\n        resetButton.addEventListener('click', initializeApp);\n\n        // Initial setup when the page loads\n        window.addEventListener('load', initializeApp);\n\n    </script>\n\n</body>\n</html>"}, {"title": "", "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "spec": "The video features the song 'Never Gonna Give You Up,' and is associated with the internet phenomenon known as 'Rickrolling.'\n\nBuild me an interactive web app to teach users about avoiding unwanted experiences, and to help them identify potential 'Rickrolls' in the future.\n\nSPECIFICATIONS:\n\n1.  The app should present the user with a series of fake links.\n\n2.  The app must somehow indicate that clicking one of the fake links will lead to an unwanted experience. For example, the app could show a button that says 'Click me to find out more' that, if clicked, navigates the user to a screen that says 'You've been Rickrolled'.\n\n3.  The app must challenge the user to distinguish between safe and unsafe links. If the user correctly identifies an unsafe link, they should be rewarded with positive feedback. If the user incorrectly identifies a safe link as unsafe, they should be given constructive criticism.\n\n4.  The app should track the user's progress. The app should record the number of unsafe links that the user correctly identifies, and the number of safe links that the user incorrectly identifies as unsafe.\n\n5.  The app should provide a summary of the user's performance at the end of the game. The app should show the number of unsafe links that the user correctly identified, and the number of safe links that the user incorrectly identified as unsafe.\n\nProvide the code as a single, self-contained HTML document. All styles and scripts must be inline. In the result, encase the code between \"```\" and \"```\" for easy parsing.", "code": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Rickroll Prevention Training</title>\n    <style>\n        body {\n            font-family: sans-serif;\n            line-height: 1.6;\n            padding: 20px;\n            background-color: #f4f4f4;\n            color: #333;\n        }\n        #appContainer {\n            max-width: 700px;\n            margin: 20px auto;\n            background-color: #fff;\n            padding: 30px;\n            border-radius: 8px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        h1, h2 {\n            color: #c0392b; /* <PERSON> hair color? :) */\n            text-align: center;\n            margin-bottom: 20px;\n        }\n        .instructions, .summary-text {\n            margin-bottom: 20px;\n            text-align: center;\n            font-size: 1.1em;\n        }\n        .rickroll-example {\n            background-color: #ecf0f1;\n            padding: 15px;\n            border-radius: 5px;\n            margin-bottom: 30px;\n            text-align: center;\n        }\n        .rickroll-example button {\n            padding: 10px 20px;\n            font-size: 1em;\n            cursor: pointer;\n            background-color: #3498db;\n            color: white;\n            border: none;\n            border-radius: 4px;\n            transition: background-color 0.3s ease;\n        }\n        .rickroll-example button:hover {\n            background-color: #2980b9;\n        }\n        #rickrollMessage {\n            margin-top: 15px;\n            padding: 10px;\n            background-color: #e74c3c;\n            color: white;\n            border-radius: 4px;\n            font-weight: bold;\n        }\n        .link-item {\n            border: 1px solid #ddd;\n            padding: 15px;\n            margin-bottom: 15px;\n            border-radius: 5px;\n            background-color: #f9f9f9;\n        }\n        .link-text {\n            display: block;\n            font-size: 1.2em;\n            color: #2980b9; /* Looks like a link */\n            margin-bottom: 5px;\n            /* text-decoration: underline; */ /* Avoid making it actually clickable */\n            cursor: default; /* Don't imply clickability */\n        }\n        .link-url-hint {\n            display: block;\n            font-style: italic;\n            color: #7f8c8d;\n            margin-bottom: 10px;\n            font-size: 0.9em;\n            word-break: break-all;\n        }\n        .action-buttons button {\n            padding: 8px 15px;\n            margin-right: 10px;\n            cursor: pointer;\n            border: 1px solid #ccc;\n            border-radius: 4px;\n            font-size: 0.9em;\n        }\n        .action-buttons button.safe-btn {\n            background-color: #2ecc71;\n            color: white;\n            border-color: #27ae60;\n        }\n        .action-buttons button.unsafe-btn {\n            background-color: #e74c3c;\n            color: white;\n            border-color: #c0392b;\n        }\n         .action-buttons button:hover {\n            opacity: 0.8;\n        }\n        .feedback {\n            margin-top: 10px;\n            padding: 10px;\n            border-radius: 4px;\n            font-weight: bold;\n            text-align: center;\n        }\n        .feedback.correct {\n            background-color: #dff0d8;\n            color: #3c763d;\n            border: 1px solid #d6e9c6;\n        }\n        .feedback.incorrect {\n            background-color: #f2dede;\n            color: #a94442;\n            border: 1px solid #ebccd1;\n        }\n        .score-area {\n            margin-top: 20px;\n            padding: 15px;\n            background-color: #ecf0f1;\n            border-radius: 5px;\n            text-align: center;\n            font-size: 1.1em;\n        }\n        .score-area span {\n            font-weight: bold;\n            margin: 0 10px;\n        }\n        #summaryArea {\n            text-align: center;\n            margin-top: 30px;\n        }\n        #summaryArea h2 {\n            color: #2c3e50;\n        }\n        #summaryArea button {\n             padding: 12px 25px;\n            font-size: 1.1em;\n            cursor: pointer;\n            background-color: #16a085;\n            color: white;\n            border: none;\n            border-radius: 4px;\n            transition: background-color 0.3s ease;\n            margin-top: 20px;\n        }\n         #summaryArea button:hover {\n             background-color: #117a65;\n         }\n        .hidden {\n            display: none;\n        }\n    </style>\n</head>\n<body>\n\n    <div id=\"appContainer\">\n        <h1>Rickroll Prevention Training</h1>\n\n        <!-- Specification 2: Rickroll Example -->\n        <div class=\"rickroll-example\">\n            <p>First, let's see what happens when you click a suspicious link unprepared. This button simulates that experience.</p>\n            <button id=\"rickrollButton\">Click me to find out more!</button>\n            <div id=\"rickrollMessage\" class=\"hidden\">\n                <p>💥 You've been Rickrolled! 💥</p>\n                <p>(Well, simulated anyway. The goal is to NOT see messages like this unexpectedly!)</p>\n            </div>\n        </div>\n\n        <hr>\n\n        <!-- Main Game Area -->\n        <div id=\"gameArea\">\n            <h2>Identify the Suspicious Links!</h2>\n            <p class=\"instructions\">Examine the link description and hint below. Decide if it looks like a potential 'Rickroll' (unsafe) or a genuine link (safe). Good luck!</p>\n\n            <div id=\"linkDisplay\">\n                <!-- Link item will be dynamically inserted here -->\n            </div>\n\n            <div id=\"feedbackArea\">\n                <!-- Feedback will appear here -->\n            </div>\n\n            <div id=\"scoreArea\" class=\"score-area\">\n                Score:\n                Correctly Spotted Unsafe: <span id=\"correctUnsafe\">0</span> |\n                Mistakenly Flagged Safe: <span id=\"incorrectSafe\">0</span>\n            </div>\n        </div>\n\n        <!-- Summary Area -->\n        <div id=\"summaryArea\" class=\"hidden\">\n            <h2>Training Complete!</h2>\n            <p class=\"summary-text\">Here's how you did:</p>\n            <div class=\"score-area\">\n                Correctly Identified Unsafe Links: <span id=\"finalCorrectUnsafe\">0</span> <br>\n                Safe Links Incorrectly Marked as Unsafe: <span id=\"finalIncorrectSafe\">0</span>\n            </div>\n            <p class=\"summary-text\" id=\"summaryMessage\"></p>\n            <button id=\"restartButton\">Play Again</button>\n        </div>\n\n    </div>\n\n    <script>\n        // --- Game Data ---\n        const allLinks = [\n            { text: \"Check out this hilarious cat video!\", urlHint: \"https://youtu.be/oHg5SJYRHA0\", isUnsafe: false, explanation: \"Standard YouTube links are usually safe, though always check the title/thumbnail preview if possible.\" },\n            { text: \"Free Bitcoin Giveaway - Click Now!\", urlHint: \"https://bit.ly/3xYzAbC\", isUnsafe: true, explanation: \"Shortened URLs (like bit.ly) combined with too-good-to-be-true offers are highly suspicious. They hide the true destination.\" },\n            { text: \"Never Gonna Give You Up - Official Music Video\", urlHint: \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\", isUnsafe: true, explanation: \"This is the classic Rickroll URL! Memorizing 'dQw4w9WgXcQ' is the ultimate defense.\" },\n            { text: \"Important Account Security Update\", urlHint: \"https://your-bank.secure-login.com/update\", isUnsafe: true, explanation: \"Phishing attempt! Banks rarely link directly to login pages from emails. The URL looks fishy ('secure-login.com' added to 'your-bank'). Always go directly to your bank's site.\" },\n            { text: \"Learn Programming Basics - Free Course\", urlHint: \"https://www.codecademy.com/learn/learn-how-to-code\", isUnsafe: false, explanation: \"Links to well-known educational sites are generally safe. The URL matches the description.\" },\n            { text: \"My Vacation Photos!\", urlHint: \"https://tinyurl.com/VacayPics2024\", isUnsafe: true, explanation: \"Another shortened URL. While sometimes legitimate for sharing, be extra cautious, especially if unexpected. Could be anything!\" },\n            { text: \"Download the Latest Game Patch\", urlHint: \"https://official-game-site.com/downloads/patch1.exe\", isUnsafe: false, explanation: \"Looks plausible if you trust the 'official-game-site.com' domain. Downloading .exe files always requires caution, but the link itself seems appropriate here.\" },\n            { text: \"You Won A Free iPhone!\", urlHint: \"http://get-your-prize.xyz/claim?id=12345\", isUnsafe: true, explanation: \"Unsolicited prize claims are almost always scams or lead to malware/spam. Uncommon TLDs like '.xyz' are also a red flag.\" },\n             { text: \"Collaborative Project Document\", urlHint: \"https://docs.google.com/document/d/1aBcD_eFgH...\", isUnsafe: false, explanation: \"Google Docs links are common for collaboration and generally safe if expected from a known contact.\" },\n             { text: \"Scientific Breakthrough Article\", urlHint: \"https://www.nature.com/articles/s41586-023-06900-x\", isUnsafe: false, explanation: \"Links to reputable scientific journals like 'nature.com' are typically safe.\" },\n             { text: \"Never Gonna Give You Up BUT it's a research paper PDF\", urlHint: \"https://definitely-not-a-rickroll.io/research.pdf\", isUnsafe: true, explanation: \"The text tries *too* hard to convince you it's NOT a Rickroll. Suspicious domain (.io can be legit, but combined with text...). High Rickroll probability!\" }\n        ];\n\n        let gameLinks = []; // Will hold the shuffled links for the current game\n        let currentLinkIndex = 0;\n        let correctUnsafeCount = 0;\n        let incorrectSafeCount = 0;\n\n        // --- DOM Elements ---\n        const rickrollButton = document.getElementById('rickrollButton');\n        const rickrollMessage = document.getElementById('rickrollMessage');\n        const gameArea = document.getElementById('gameArea');\n        const linkDisplay = document.getElementById('linkDisplay');\n        const feedbackArea = document.getElementById('feedbackArea');\n        const scoreCorrectUnsafe = document.getElementById('correctUnsafe');\n        const scoreIncorrectSafe = document.getElementById('incorrectSafe');\n        const summaryArea = document.getElementById('summaryArea');\n        const finalCorrectUnsafe = document.getElementById('finalCorrectUnsafe');\n        const finalIncorrectSafe = document.getElementById('finalIncorrectSafe');\n        const summaryMessage = document.getElementById('summaryMessage');\n        const restartButton = document.getElementById('restartButton');\n\n        // --- Utility Functions ---\n        function shuffleArray(array) {\n            for (let i = array.length - 1; i > 0; i--) {\n                const j = Math.floor(Math.random() * (i + 1));\n                [array[i], array[j]] = [array[j], array[i]]; // Swap elements\n            }\n        }\n\n        function hideElement(el) {\n            el.classList.add('hidden');\n        }\n\n        function showElement(el) {\n            el.classList.remove('hidden');\n        }\n\n        // --- Game Logic ---\n        function displayLink(index) {\n            feedbackArea.innerHTML = ''; // Clear previous feedback\n            hideElement(feedbackArea);\n\n            if (index >= gameLinks.length) {\n                showSummary();\n                return;\n            }\n\n            const link = gameLinks[index];\n            linkDisplay.innerHTML = `\n                <div class=\"link-item\">\n                    <span class=\"link-text\">${link.text}</span>\n                    <span class=\"link-url-hint\">Hint: ${link.urlHint}</span>\n                    <div class=\"action-buttons\">\n                        <button class=\"safe-btn\" onclick=\"checkAnswer(false)\">Mark as Safe</button>\n                        <button class=\"unsafe-btn\" onclick=\"checkAnswer(true)\">Mark as Suspicious (Unsafe)</button>\n                    </div>\n                </div>\n            `;\n        }\n\n        function checkAnswer(userMarkedUnsafe) {\n            const currentLink = gameLinks[currentLinkIndex];\n            const actuallyUnsafe = currentLink.isUnsafe;\n            let feedbackHTML = '';\n            let isCorrect = false;\n\n            if (userMarkedUnsafe === actuallyUnsafe) {\n                // Correct identification\n                isCorrect = true;\n                if (actuallyUnsafe) {\n                    correctUnsafeCount++;\n                    feedbackHTML = `<div class=\"feedback correct\">Correct! This link looked suspicious. ${currentLink.explanation || ''}</div>`;\n                } else {\n                     feedbackHTML = `<div class=\"feedback correct\">Correct! This link was likely safe. ${currentLink.explanation || ''}</div>`;\n                }\n            } else {\n                // Incorrect identification\n                isCorrect = false;\n                if (actuallyUnsafe) {\n                     feedbackHTML = `<div class=\"feedback incorrect\">Oops! That was actually unsafe. You might have been Rickrolled! ${currentLink.explanation || ''}</div>`;\n                } else {\n                    incorrectSafeCount++;\n                     feedbackHTML = `<div class=\"feedback incorrect\">Careful! This link was probably safe. You flagged it unnecessarily. ${currentLink.explanation || ''}</div>`;\n                }\n            }\n\n            feedbackArea.innerHTML = feedbackHTML;\n            showElement(feedbackArea);\n            updateScoreDisplay();\n\n            // Disable buttons after choice\n            const buttons = linkDisplay.querySelectorAll('.action-buttons button');\n            buttons.forEach(button => button.disabled = true);\n\n            // Move to the next link after a delay\n            currentLinkIndex++;\n            setTimeout(() => {\n                displayLink(currentLinkIndex);\n            }, 2500); // Show feedback for 2.5 seconds\n        }\n\n        function updateScoreDisplay() {\n            scoreCorrectUnsafe.textContent = correctUnsafeCount;\n            scoreIncorrectSafe.textContent = incorrectSafeCount;\n        }\n\n        function showSummary() {\n            hideElement(gameArea);\n            showElement(summaryArea);\n\n            finalCorrectUnsafe.textContent = correctUnsafeCount;\n            finalIncorrectSafe.textContent = incorrectSafeCount;\n\n            let message = \"\";\n            const totalUnsafe = gameLinks.filter(link => link.isUnsafe).length;\n            const accuracy = totalUnsafe > 0 ? (correctUnsafeCount / totalUnsafe * 100).toFixed(0) : 100;\n\n            if (accuracy >= 80 && incorrectSafeCount <= 1) {\n                message = \"Excellent work! You have a sharp eye for suspicious links. Keep those instincts sharp!\";\n            } else if (accuracy >= 50 && incorrectSafeCount <= 3) {\n                message = \"Good job! You spotted quite a few tricky links. Pay a little extra attention to the hints and common patterns.\";\n            } else {\n                message = \"You're learning! Review the explanations for the links you missed. Practice makes perfect in avoiding unwanted internet surprises!\";\n            }\n             if (totalUnsafe > 0) {\n                 message += ` You correctly identified ${accuracy}% of the unsafe links presented.`;\n             } else {\n                 message += \" No unsafe links were presented in this round.\"\n             }\n\n            summaryMessage.textContent = message;\n        }\n\n        function startGame() {\n            // Reset state\n            currentLinkIndex = 0;\n            correctUnsafeCount = 0;\n            incorrectSafeCount = 0;\n            gameLinks = [...allLinks]; // Create a copy\n            shuffleArray(gameLinks);\n\n            // Reset UI\n            hideElement(summaryArea);\n            showElement(gameArea);\n            hideElement(feedbackArea);\n            feedbackArea.innerHTML = '';\n            updateScoreDisplay();\n            hideElement(rickrollMessage); // Ensure example message is hidden\n            rickrollButton.disabled = false; // Re-enable example button\n\n            // Start the game\n            displayLink(currentLinkIndex);\n        }\n\n        // --- Event Listeners ---\n        rickrollButton.addEventListener('click', () => {\n            showElement(rickrollMessage);\n            rickrollButton.disabled = true; // Prevent multiple clicks\n        });\n\n        restartButton.addEventListener('click', startGame);\n\n        // --- Initial Load ---\n        document.addEventListener('DOMContentLoaded', startGame);\n\n    </script>\n\n</body>\n</html>"}]