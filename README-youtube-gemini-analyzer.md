# Gerador de Apps Interativos de Aprendizagem - YouTube + Gemini

Este projeto converte vídeos do YouTube em aplicativos web interativos de aprendizagem usando a API do Google Gemini. A aplicação analisa o conteúdo do vídeo e gera automaticamente um app HTML/CSS/JavaScript personalizado para reforçar os conceitos apresentados.

## 🚀 Funcionalidades

### 🎯 **Dois Modos de Operação**

1. **Gerar App Interativo** (Padrão)
   - Analisa o vídeo do YouTube automaticamente
   - Gera uma especificação pedagógica detalhada
   - Cria um aplicativo web interativo completo
   - Interface com 3 abas: App Interativo, Código HTML, Especificação

2. **Análise Personalizada**
   - Permite inserir prompts customizados
   - Análise direcionada do conteúdo do vídeo
   - Resposta formatada em Markdown

### 🔧 **Processo de Geração de Apps (2 Etapas)**

#### **Etapa 1: Geração da Especificação**
- **Modelo**: Gemini 2.0 Flash
- **Função**: Analisa o vídeo e cria especificação pedagógica
- **Saída**: Documento detalhado com objetivos e mecânicas do app

#### **Etapa 2: Geração do Código**
- **Modelo**: Gemini 2.5 Pro Preview
- **Função**: Converte especificação em código HTML funcional
- **Saída**: App web completo e responsivo

### 📱 **Interface Interativa**

- **Abas de Visualização**:
  - **App Interativo**: Iframe com o aplicativo gerado
  - **Código HTML**: Editor para modificar o código
  - **Especificação**: Visualização e edição da especificação

- **Funcionalidades de Edição**:
  - Editar código HTML em tempo real
  - Modificar especificação e regenerar código
  - Atualização instantânea do app

## 🛠️ **Como Usar**

### **Pré-requisitos**
- Chave da API do Google Gemini
- Navegador web moderno
- Conexão com internet

### **Passos**

1. **Abra o arquivo** `youtube-gemini-analyzer.html` no navegador

2. **Insira os dados**:
   - URL do vídeo do YouTube
   - Chave da API Gemini

3. **Escolha o modo**:
   - **App Interativo**: Para gerar aplicativo educacional
   - **Análise Personalizada**: Para análise customizada

4. **Clique em "Gerar"** e aguarde o processamento

5. **Explore o resultado**:
   - Teste o app interativo na aba "App Interativo"
   - Visualize/edite o código na aba "Código HTML"
   - Revise a especificação na aba "Especificação"

## 🎓 **Exemplos de Apps Gerados**

### **Vídeo sobre Música**
- **App**: Teclado interativo para acordes
- **Funcionalidades**: Tocar notas, visualizar inversões, teoria musical

### **Vídeo sobre Matemática**
- **App**: Calculadora de fractais
- **Funcionalidades**: Iterações, zoom, visualização interativa

### **Vídeo sobre Idiomas**
- **App**: Construtor de caracteres chineses
- **Funcionalidades**: Arrastar radicais, formar caracteres, pronúncia

## 🔧 **Tecnologias Utilizadas**

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **IA**: Google Gemini 2.0 Flash + 2.5 Pro Preview
- **Bibliotecas**: Marked.js (conversão Markdown)
- **APIs**: YouTube (via URL), Google Generative AI

## 📋 **Estrutura do Código**

### **Prompts Principais**
```javascript
// Prompt para geração de especificação
const SPEC_FROM_VIDEO_PROMPT = `Você é um pedagogo e designer...`;

// Adendo para especificação
const SPEC_ADDENDUM = `O aplicativo deve ser totalmente responsivo...`;
```

### **Funções Principais**
- `generateInteractiveApp()`: Processo completo de geração
- `generateSpecFromVideo()`: Primeira etapa (especificação)
- `generateCodeFromSpec()`: Segunda etapa (código)
- `sendToGemini()`: Comunicação com API
- `updateAppDisplay()`: Atualização do iframe

## 🎨 **Características dos Apps Gerados**

- **Responsivos**: Funcionam em desktop e mobile
- **Auto-contidos**: HTML com CSS e JS inline
- **Interativos**: Elementos clicáveis, animações, feedback
- **Educacionais**: Focados em reforçar conceitos do vídeo
- **Acessíveis**: Interface intuitiva e clara

## 🔒 **Segurança**

- Iframe com sandbox para execução segura
- Validação de URLs do YouTube
- Tratamento de erros da API
- Limpeza automática de blob URLs

## 📝 **Limitações**

- Requer chave da API Gemini válida
- Dependente da qualidade do vídeo de entrada
- Limitado a vídeos públicos do YouTube
- Processamento pode levar alguns minutos

## 🚀 **Melhorias Futuras**

- [ ] Suporte a outros provedores de vídeo
- [ ] Templates de apps pré-definidos
- [ ] Exportação de apps como arquivos
- [ ] Histórico de apps gerados
- [ ] Compartilhamento de apps
- [ ] Integração com sistemas de aprendizagem

## 📄 **Licença**

Este projeto está sob a licença Apache 2.0. Veja o arquivo LICENSE para mais detalhes.

---

**Desenvolvido com ❤️ usando Google Gemini AI**
